import { createSelector } from 'reselect'
import { initialState } from './reducer'

const selectReviewList = state => state.get('review', initialState).get('list')
const selectIsLoading = state => state.get('review', initialState).get('isLoading')
const selectSummaryReview = state => state.get('review', initialState).get('summaryReview')
const selectReviewPagination = state => state.get('review', initialState).get('pagination')
const selectReviewGroupByRating = state => state.get('review', initialState).get('groupByRating')
const selectCategorySummary = state => state.get('review', initialState).get('categorySummary')

export const makeSelectReviewList = () => createSelector(
  selectReviewList,
  list => list ? list.toJS() : []
)

export const makeSelectIsLoading = () => createSelector(
  selectIsLoading,
  isLoading => isLoading
)

export const makeSelectSummaryReview = () => createSelector(
  selectSummaryReview,
  summaryReview => summaryReview ? summaryReview.toJS() : {}
)

export const makeSelectReviewPagination = () => createSelector(
  selectReviewPagination,
  pagination => pagination ? pagination.toJS() : {}
)

export const makeSelectReviewGroupByRating = () => createSelector(
  selectReviewGroupByRating,
  reviewGroupByRating => reviewGroupByRating ? reviewGroupByRating.toJS() : []
)

export const makeSelectCategorySummary = () => createSelector(
  selectCategorySummary,
  categorySummary => categorySummary ? categorySummary.toJS() : {}
)

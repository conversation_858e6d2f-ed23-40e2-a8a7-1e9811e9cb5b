import { fromJ<PERSON> } from 'immutable'
import {
  BOOKING_REVIEWS_REQUEST,
  BOOKING_REVIEWS_SUCCESS,
  BOOKING_REVIEWS_FAILED,
  THERAPIST_REVIEWS_REQUEST,
  THERAPIST_REVIEWS_SUCCESS,
  THERAPIST_REVIEWS_FAILED,
  CUSTOMER_REVIEWS_REQUEST,
  CUSTOMER_REVIEWS_SUCCESS,
  CUSTOMER_REVIEWS_FAILED,
  RESET_REVIEW
} from './actions'

export const initialState = fromJS({
  list: [],
  isLoading: false,
  summaryReview: {},
  pagination: {},
  groupByRating: [],
  categorySummary: {}
})

function reviewReducer (state = initialState, action) {
  switch (action.type) {
    case BOOKING_REVIEWS_REQUEST:
    case THERAPIST_REVIEWS_REQUEST:
    case CUSTOMER_REVIEWS_REQUEST:
      return state.set('isLoading', fromJS(true))
    case BOOKING_REVIEWS_SUCCESS:
      return state
        .set('isLoading', fromJS(false))
        .set('list', fromJS(action.payload))
    case THERAPIST_REVIEWS_SUCCESS:
    case CUSTOMER_REVIEWS_SUCCESS: {
      const { data, ...pagination } = action.payload.reviews
      return state
        .set('isLoading', fromJS(false))
        .set('list', fromJS(data))
        .set('pagination', fromJS(pagination))
        .set('groupByRating', fromJS(action.payload.detail))
        .set('summaryReview', fromJS(action.payload.summaryReview || {}))
        .set('categorySummary', fromJS(action.payload.categorySummary || {}))
    }
    case BOOKING_REVIEWS_FAILED:
    case THERAPIST_REVIEWS_FAILED:
    case CUSTOMER_REVIEWS_FAILED:
      return state.set('isLoading', fromJS(false))
    case RESET_REVIEW:
      return initialState
    default:
      return state
  }
}

export default reviewReducer

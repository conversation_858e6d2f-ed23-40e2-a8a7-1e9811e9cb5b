import React, {
  Fragment, useEffect, useRef, useState
} from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { Helmet } from 'react-helmet'
import { FormattedMessage } from 'react-intl'

import { createStructuredSelector } from 'reselect'
import { compose } from 'redux'

import _ from 'lodash'
import moment from 'moment-timezone'
import defaultProfile from 'assets/img/default-profile.png'
import TableList, { LeftColumn } from 'components/TableList'
import Pagination from 'components/Pagination'
import Search from 'components/AsyncSearchBox'
import ReviewItem from 'components/ReviewItem'
import { DateRangePicker } from 'react-dates'
import searchIcon from 'assets/img/search.svg'
import {
  INPUT_DELAY,
  MAXIMUMDATE,
  MINIMUMDATE,
  ROLES
} from 'utils/constants'
import { OutOfRangeDate } from 'utils/helper'
import { makeSelectCurrentUserData } from 'containers/Account/selectors'
import {
  makeSelectBookingReviewFilter,
  makeSelectBookingReviewList,
  makeSelectBookingReviewPagination,
  makeSelectIsLoading,
  makeSelectIsReload
} from './selectors'

import {
  changeFilter,
  exportBookingReviewList,
  getAll,
  resetBookingReview
} from './actions'
import PageTitle from '../PageTitle'
import messages from './messages'
import './style.scss'

const BookingReview = ({
  bookingReviews,
  pagination,
  fetchAll,
  resetAll,
  filter,
  updateFilter,
  isLoading,
  downloadBookingReviewsList,
  account
}) => {
  const isAgent = account.role === ROLES.AGENT
  useEffect(() => {
    updateFilter({
      ...filter, ...selectedDate
    })
    fetchAll()
    return () => {
      resetAll()
    }
  }, [])
  const [selectedDate, setSelectedDate] = useState({
    start: moment().startOf('d').toISOString(),
    end: moment().endOf('d').toISOString()
  })
  const searchBoxRef = useRef(null)
  const [focusedInput, setFocusedInput] = useState(null)

  const changePage = page => searchBookingReview({ ...filter, page })

  const onChangeDate = ({ startDate, endDate }) => {
    const isOutOfRangeDate = OutOfRangeDate(document.getElementById('bookingReviewEndDateId').value, filter)
    const start = startDate ? startDate.startOf('day').toISOString() : selectedDate.start
    const end = endDate ? endDate.endOf('day').toISOString() : isOutOfRangeDate
    setSelectedDate({ start, end })
    updateFilter({
      ...filter, start, end, page: 1
    })
  }
  const onChangeKeyword = keyword => {
    searchBookingReview({ ...filter, bookingId: keyword, page: 1 })
  }

  const searchBookingReview = query => {
    updateFilter(query)
    fetchAll()
  }
  return (
    <Fragment>
      <Helmet title='レビュー管理' />
      <PageTitle title={messages.header} />
      <div className='filter-container review-filter'>
        <div className='filter-container-input'>
          <div className='filter-container-input-inside'>
            <div className='search-box'>
              <Search
                searchBoxRef={searchBoxRef}
                key={filter.keyword}
                value={filter.keyword}
                onChange={_.debounce(value => onChangeKeyword(value || ''), Number(INPUT_DELAY))}
              />
            </div>
            <div className='search-icon-container' onClick={() => searchBookingReview(filter)}>
              <div className='image' >
                <img src={searchIcon} alt='null' />
              </div>
            </div>
          </div>

        </div>
        <div className='filter-container-input'>
          <DateRangePicker
            monthFormat={moment.locale() === 'ja' ? 'YYYY年MM月' : 'MMMM YYYY'}
            startDate={selectedDate.start ? moment(selectedDate.start) : null}
            startDateId='bookingReviewStartDateId'
            endDate={selectedDate.end ? moment(selectedDate.end) : null}
            endDateId='bookingReviewEndDateId'
            onFocusChange={focusingInput => setFocusedInput(focusingInput)}
            onDatesChange={onChangeDate}
            focusedInput={focusedInput}
            minimumNights={0}
            hideKeyboardShortcutsPanel
            isOutsideRange={date => date.isAfter(MAXIMUMDATE) ||
              date.isBefore(MINIMUMDATE)
            }
          />
        </div>
        <div className='clear-filter-container'>
          <button onClick={() => searchBookingReview(filter)} type='button' className='apply-filter-button'>
            <span><FormattedMessage {...messages.applyFilter} /></span>
          </button>
        </div>
        <div className='export-csv-report-container  ml-auto' onClick={() => downloadBookingReviewsList(filter)}>
          <button
            type='button'
            className='export-csv-button'
            disabled={isLoading}
          >
            <i className='fa fa-sign-in' aria-hidden='true'></i>
            {' '}
            <span><FormattedMessage {...messages.exportReview} /></span>
          </button>
        </div>
      </div>
      <TableList>
        <LeftColumn>
          <table className='table table-list table-review'>
            <thead>
              <tr>
                <th width='15%'><FormattedMessage {...messages.bookingId} /></th>
                <th width='15%'><FormattedMessage {...messages.createdDate} /></th>
                <th className='user-review'>
                  <div style={{ width: '15%' }}>
                    <FormattedMessage {...messages.typeAccount} />
                  </div>
                  <div style={{ width: '20%' }}><FormattedMessage {...messages.name} /></div>
                  <div style={{ width: '15%' }}><FormattedMessage {...messages.id} /></div>
                  <div style={{ width: '25%' }}><FormattedMessage {...messages.review} /></div>
                </th>
              </tr>
            </thead>
            <tbody>
              {bookingReviews.map(item => (
                <tr key={item._id}>
                  <td>
                    <a className='text-primary' href={`booking/${item._id}`} target='_blank'>
                      {item._id}
                    </a>
                  </td>
                  <td>
                    {(item.dateBooking) ? moment(item.dateBooking).format('YYYY-MM-DD HH:mm') : '-'}
                  </td>
                  {item.data.length > 0 ? item.data.map(child => (
                    <td key={child._id} className={item.data.length > 1 ? 'split-review user-review' : 'user-review'}>
                      <div style={{ width: '15%' }} className='text-capitalize'>{_.get(child, 'reviewer.role')}</div>
                      <div style={{ width: '20%' }}>
                        <img src={_.get(child, 'reviewer.profilePicture.url') || defaultProfile} className='avatar-in-list' />
                        {_.get(child, 'reviewer.fullName') || _.get(child, 'reviewer.name') || '「退会済ユーザー」' }
                      </div>
                      <div style={{ width: '15%' }}>
                        {
                          _.get(child, 'reviewer.id')
                            ? isAgent && _.get(child, 'reviewer.role') === 'customer'
                              ? <span>{_.get(child, 'reviewer.id')}</span>
                              : (
                                <a
                                  className='text-primary'
                                  href={_.get(child, 'reviewer.role') === 'therapist'
                                    ? `therapist/${_.get(child, 'reviewer.id')}`
                                    : `customer/${_.get(child, 'reviewer.id')}`
                                  }
                                  target='_blank'
                                >
                                  {_.get(child, 'reviewer.id')}
                                </a>
                              )
                            : <span>-</span>
                        }
                      </div>
                      { _.get(child, 'rating') ? (
                        <div style={{ width: '50%' }}>
                          <ReviewItem
                            review={child}
                            user={{
                              ...child.reviewer,
                              role: child.reviewer.role
                            }}
                            renderMode='table'
                            type='BookingReviewTable'
                          />
                        </div>
                      )
                        : (
                          <div style={{ width: '50%' }}>
                            <FormattedMessage {...messages.notReview} />
                          </div>
                        )
                      }
                    </td>
                  ))
                    : <td></td>
                  }
                </tr>
              ))}
            </tbody>
          </table>
          <Pagination pagination={pagination} onChange={changePage} />
        </LeftColumn>
      </TableList>
    </Fragment>
  )
}

BookingReview.propTypes = {
  bookingReviews: PropTypes.array,
  pagination: PropTypes.object,
  fetchAll: PropTypes.func,
  resetAll: PropTypes.func,
  onChangeKeyword: PropTypes.func,
  filter: PropTypes.any,
  updateFilter: PropTypes.func,
  keyword: PropTypes.any,
  isLoading: PropTypes.bool,
  downloadBookingReviewsList: PropTypes.func,
  account: PropTypes.any
}
BookingReview.defaultProps = {
  filter: {}
}
const mapStateToProps = createStructuredSelector({
  bookingReviews: makeSelectBookingReviewList(),
  pagination: makeSelectBookingReviewPagination(),
  filter: makeSelectBookingReviewFilter(),
  isLoading: makeSelectIsLoading(),
  isReload: makeSelectIsReload(),
  account: makeSelectCurrentUserData()
})

const mapDispatchToProps = dispatch => ({
  fetchAll: () => dispatch(getAll()),
  resetAll: () => dispatch(resetBookingReview()),
  updateFilter: filter => dispatch(changeFilter(filter)),
  downloadBookingReviewsList: () => dispatch(exportBookingReviewList())
})

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps
)

export default compose(withConnect)(BookingReview)

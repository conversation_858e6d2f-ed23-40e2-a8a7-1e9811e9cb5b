import { createSelector } from 'reselect'
import { initialState } from './reducer'

const selectCustomerList = state => state.get('customer', initialState).get('list')
const selectCBlacklist = state => state.get('customer', initialState).get('blacklist')
const selectIsLoading = state => state.get('customer', initialState).get('isLoading')
const selectIsReload = state => state.get('customer', initialState).get('isReload')
const selectCustomerPagination = state => state.get('customer', initialState).get('pagination')
const selectSelectedCustomer = state => state.get('customer', initialState).get('selectedCustomer')
const selectCustomerFilter = state => state.get('customer', initialState).get('filter')

export const makeSelectCustomerList = () => createSelector(
  selectCustomerList,
  list => list ? list.toJS() : []
)

export const makeSelectBlackist = () => createSelector(
  selectCBlacklist,
  list => list ? list.toJS() : []
)

export const makeSelectIsLoading = () => createSelector(
  selectIsLoading,
  isLoading => isLoading
)

export const makeSelectIsReload = () => createSelector(
  selectIsReload,
  isReload => isReload
)

export const makeSelectCustomerPagination = () => createSelector(
  selectCustomerPagination,
  pagination => pagination ? pagination.toJS() : {}
)

export const makeSelectedCustomer = () => createSelector(
  selectSelectedCustomer,
  customer => customer ? customer.toJS() : {}
)

export const makeSelectCustomerFilter = () => createSelector(
  selectCustomerFilter,
  filter => filter ? filter.toJS() : {}
)

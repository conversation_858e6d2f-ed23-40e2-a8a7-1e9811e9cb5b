import {
  $get, $put, baseUri
} from 'utils/Api'

export const getConfigurations = async () => {
  const { data } = await $get({
    url: `${baseUri.configurationService}web/admin/configurations`
  })
  return data
}

export const updateConfiguration = async ({ _id, name, ...payload }) => {
  const { data } = await $put({
    url: `${baseUri.configurationService}web/admin/configurations/${name}`,
    data: payload
  })
  return data
}

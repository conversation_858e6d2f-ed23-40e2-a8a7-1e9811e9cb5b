import {
  $get, $post, $put, baseUri
} from 'utils/Api'

export async function getAgent (params) {
  const { data } = await $get({
    url: `${baseUri.accountService}web/admin/agent`,
    params
  })
  return data
}

export async function getAgentDetail (id) {
  const { data } = await $get({
    url: `${baseUri.accountService}web/admin/agent/${id}`
  })
  return data
}

export async function addAgent (payload) {
  const result = await $post({
    url: `${baseUri.accountService}web/admin/agent`,
    data: payload
  })
  return result
}

export async function updateMe (payload) {
  const data = await $put({
    url: `${baseUri.accountService}web/agent/me`,
    data: payload
  })
  return data
}

export const cooperateAgent = async payload => {
  const { data, error } = await $put({
    url: `${baseUri.accountService}web/admin/agent/linking`,
    data: payload
  })
  if (error) throw error
  return data
}

export async function updateBank (payload) {
  const result = await $post({
    url: `${baseUri.accountService}web/agent/bank`,
    data: payload
  })
  return result
}

export async function getBankAgentById ({ id }) {
  const { data } = await $get({
    url: `${baseUri.accountService}web/admin/agent/${id}/bank`
  })
  return data
}

import {
  $get, $post, $put, $patch, $download, baseUri
} from 'utils/Api'

export async function getSummary (params) {
  const { data } = await $get({
    url: `${baseUri.bookingService}web/admin/summary`,
    params
  })
  return data
}

export async function getAgentSummary (params) {
  const { data } = await $get({
    url: `${baseUri.bookingService}web/admin/agent/summary`,
    params
  })
  return data
}

export async function getBookings (params) {
  const { data } = await $get({
    url: `${baseUri.bookingService}web/admin/booking`,
    params
  })
  return data
}

export async function getBookingById (id) {
  const { data } = await $get({
    url: `${baseUri.bookingService}web/admin/booking/${id}`
  })
  return data
}

export async function createBooking (data) {
  const result = await $post({
    url: `${baseUri.bookingService}web/admin/booking`,
    data
  })
  return result
}

export async function editBooking (data) {
  const result = await $put({
    url: `${baseUri.bookingService}web/admin/booking/${data._id}/change-price`,
    data
  })
  return result
}

export async function cancelBooking ({ id, data }) {
  const result = await $post({
    url: `${baseUri.bookingService}web/admin/booking/${id}/status/canceled`,
    data
  })
  return result
}

export async function assignBooking ({ id, data }) {
  const result = await $post({
    url: `${baseUri.bookingService}web/admin/booking/${id}/assignment`,
    data
  })
  return result
}

export async function createBookingMenus (data) {
  const result = await $post({
    url: `${baseUri.bookingService}web/admin/menus`,
    data,
    config: {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  })
  return result
}

export async function updateBookingMenus ({ id, data }) {
  const result = await $put({
    url: `${baseUri.bookingService}web/admin/menus/${id}`,
    data,
    config: {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    }
  })
  return result
}

export async function completeBooking (id) {
  const result = await $patch({
    url: `${baseUri.bookingService}web/admin/booking/${id}/status/completed`
  })
  return result
}

export async function completeBookingPaymentStatus (id) {
  const result = await $patch({
    url: `${baseUri.bookingService}web/admin/booking/${id}/status/canceled/recharge`
  })
  return result
}

export const getCouponToken = async () => {
  const { data } = await $get({
    url: `${baseUri.bookingService}web/admin/extraSystem/coupon/token`
  })
  return data
}

export const uploadImage = async file => {
  const formData = new FormData()
  formData.append('image', file)
  const { data } = await $post({
    url: `${baseUri.bookingService}web/guest/upload/images`,
    data: formData
  })
  return data
}

export const exportCancellationReason = async ({ filename, ...params }) => {
  await $download({
    url: `${baseUri.bookingService}web/admin/booking/cancellation-reason`,
    params,
    data: { filename }
  })
}

export async function getChatMessages (id) {
  try {
    const { data } = await $get({
      url: `${baseUri.bookingService}web/admin/booking/${id}/chat-history`
    })
    return data
  } catch (e) {
    throw new Error(e)
  }
}

export async function changeReasonBooking (id) {
  const result = await $patch({
    url: `${baseUri.bookingService}web/admin/booking/${id}/reason`
  })
  return result
}
export async function revertBookingstatus (id) {
  const result = await $patch({
    url: `${baseUri.bookingService}web/admin/booking/${id}/status/revert`
  })
  return result
}

export async function updateBookingPenalty ({ bookingId, ...params }) {
  const result = await $put({
    url: `${baseUri.bookingService}web/admin/booking/${bookingId}/penalty`,
    data: params
  })
  return result
}

export async function updateBookingNote ({ bookingId, ...data }) {
  const result = await $put({
    url: `${baseUri.bookingService}web/admin/booking/${bookingId}/change-notes`,
    data
  })
  return result
}

export async function setCustomerPoint ({ customerId, type, point }) {
  const result = await $post({
    url: `${baseUri.bookingService}web/admin/customer/${customerId}/point/${type}`,
    data: { point },
    config: { allowedCodes: [422] }
  })
  return result
}

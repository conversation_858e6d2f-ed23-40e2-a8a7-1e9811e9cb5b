import {
  $get, $patch, $download, baseUri
} from 'utils/Api'

export async function getPayouts ({ payload }) {
  const params = payload
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/agent`,
    params
  })
  return data
}

export async function getPayoutDetail (id) {
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/agent/payout/${id}`
  })
  return data
}

export const markAsPaid = async data => {
  const result = await $patch({
    url: `${baseUri.payoutService}web/admin/payout/agent/status/paid`,
    data
  })
  return result
}

export const exportPayout = async () => {
  await $download({
    url: `${baseUri.payoutService}web/admin/payout/agent/export`,
    data: { filename: 'payout.csv' }
  })
}

export const exportBankTransfer = async () => {
  await $download({
    url: `${baseUri.payoutService}web/admin/payout/agent/bank-transfer/export`,
    data: { filename: 'bank-transfer.csv' }
  })
}

export async function getPayoutsByAgent ({ payload, agentId }) {
  const params = payload
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/agent/${agentId}`,
    params
  })
  return data
}

export async function exportPayoutsByAgent ({ payload }) {
  const params = payload
  await $download({
    url: `${baseUri.payoutService}web/admin/payout/agent/${params.agentId}/export`,
    params,
    data: { filename: `payout_of_${params.agentId}.csv` }
  })
}
//
export async function getPayoutIncomes ({ payload }) {
  const params = payload
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/agent/payout/${params.payoutId}/income`,
    params
  })
  return data
}

export async function getEarning (agentId) {
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/agent/${agentId}/earning`
  })
  return data
}

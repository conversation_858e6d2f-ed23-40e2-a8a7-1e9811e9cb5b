import {
  $get, $post, $download, baseUri, $patch, $put, $delete
} from 'utils/Api'
import moment from 'moment-timezone'

export async function getMe (token) {
  const result = await $get({
    url: `${baseUri.accountService}web/admin/me`,
    config: {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  })
  return result
}

export async function getAccountById (id) {
  const result = await $get({
    url: `${baseUri.accountService}web/admin/accounts/${id}`
  })
  return result
}

export async function getCustomers (payload) {
  const { data } = await $get({
    url: `${baseUri.accountService}web/admin/customer`,
    params: payload
  })
  return data
}

export async function getBlacklistOfTherapist (payload) {
  const { data } = await $get({
    url: `${baseUri.accountService}web/admin/customer/blacklist`,
    params: payload
  })
  return data
}

export async function getIdentityCard (payload) {
  const { data } = await $post({
    url: `${baseUri.accountService}web/admin/customer/identity-card`,
    data: payload
  })
  return data
}

export async function changeIdentityCardStatus ({ id, status }) {
  const { data } = await $patch({
    url: `${baseUri.accountService}web/admin/customer/${id}/identity-card/status`,
    data: { status }
  })
  return data
}

export const exportCustomersList = async payload => {
  await $download({
    url: `${baseUri.accountService}web/admin/customer/reports/csv`,
    params: payload,
    data: { filename: `customers_${moment().format('YYYY-MM-DD')}.csv` }
  })
}

export const exportBlacklistCustomers = async () => {
  await $download({
    url: `${baseUri.accountService}web/admin/customer/blacklist/export`,
    data: { filename: `blacklist-customers_${moment().format('YYYY-MM-DD')}.csv` }
  })
}

export const exportTherapist = async ({ filename, ...params }) => {
  await $download({
    url: `${baseUri.accountService}web/admin/therapist/export`,
    params,
    data: { filename }
  })
}
export const exportFavoriteTherapist = async ({ filename, ...params }) => {
  await $download({
    url: `${baseUri.accountService}web/admin/therapist/export-favorite`,
    params,
    data: { filename }
  })
}

export const updateCustomer = async ({ _id, phone }) => {
  const result = await $put({
    url: `${baseUri.accountService}web/admin/customer/${_id}`,
    data: { phone },
    config: { allowedCodes: [422] }
  })
  return result
}

export const changeCustomerStatus = async ({ id, status }) => {
  const { data, error } = await $patch({
    url: `${baseUri.accountService}web/admin/customer/${id}/status`,
    data: { status }
  })
  if (error) throw error
  return data
}

export const changeTherapistStatus = async ({ id, status }) => {
  const { data, error } = await $patch({
    url: `${baseUri.accountService}web/admin/therapist/${id}/status`,
    data: { status }
  })
  if (error) throw error
  return data
}
export const updateTherapist = async ({ _id, phone, fullName }) => {
  const result = await $put({
    url: `${baseUri.accountService}web/admin/therapist/${_id}`,
    data: { phone, fullName },
    config: { allowedCodes: [422] }
  })
  return result
}

export const setTreatmentSetting = async ({ id, status }) => {
  const { data, error } = await $put({
    url: `${baseUri.accountService}web/admin/therapist/${id}/treatment-setting`,
    data: { status }
  })
  if (error) throw error
  return data
}

export const setDisplaySetting = async ({ id, status }) => {
  const { data, error } = await $patch({
    url: `${baseUri.accountService}web/admin/therapist/${id}/show-hide`,
    data: { isShow: status }
  })
  if (error) throw error
  return data
}

export const postComment = async payload => {
  const formData = new FormData()
  payload.files && payload.files.map(i => formData.append('files[]', i))
  payload.content && formData.append('content', payload.content)
  formData.append('user.id', payload.user.id)
  formData.append('user.role', payload.user.role)
  const { data, error } = await $post({
    url: `${baseUri.accountService}web/admin/comment`,
    data: formData
  })
  if (error) throw error
  return data
}

export const paginateComment = async ({ userId, ...payload }) => {
  if (!userId) return []
  const { data } = await $get({
    url: `${baseUri.accountService}web/admin/comment/${userId}`,
    params: payload
  })
  return data
}

export const deleteComment = async id => {
  const { data } = await $delete({
    url: `${baseUri.accountService}web/admin/comment/${id}`
  })
  return data
}

export const createSalon = async ({ name, email }) => {
  const resData = await $post({
    url: `${baseUri.accountService}web/admin/salon`,
    data: { name, email },
    config: { allowedCodes: [422] }
  })

  const { data, error } = resData
  if (error) throw resData
  return data
}

export const fetchSalon = async ({ page = 1, limit = 20 } = {}) => {
  const resData = await $get({
    url: `${baseUri.accountService}web/admin/salon`,
    params: {
      page, limit
    }
  })

  const { data, error } = resData
  if (error) throw resData
  return data
}

export const fetchSalonDetail = async ({ salonId } = {}) => {
  const resData = await $get({
    url: `${baseUri.accountService}web/admin/salon/${salonId}`
  })

  const { data, error } = resData
  if (error) throw resData
  return data
}

export const fetchSalonDetailBank = async ({ salonId } = {}) => {
  const resData = await $get({
    url: `${baseUri.accountService}web/admin/salon/${salonId}/bank`
  })

  const { data, error } = resData
  if (error) throw resData
  return data
}

export const deleteSalon = async ({ salonId } = {}) => {
  const resData = await $delete({
    url: `${baseUri.accountService}web/admin/salon/${salonId}`
  })

  const { data, error } = resData
  if (error) throw resData
  return data
}

export const resendSalonInvitation = async ({ email } = {}) => {
  const resData = await $post({
    url: `${baseUri.accountService}web/admin/salon/resend-email`,
    data: { email }
  })

  const { data, error } = resData
  if (error) throw resData
  return data
}

export async function getCouponOfTherapist (payload) {
  const { data } = await $get({
    url: `${baseUri.accountService}web/admin/therapist/${payload.id}/coupon`,
    params: payload
  })
  return data
}
export async function changeStatusCouponOfTherapist (payload) {
  const { data } = await $patch({
    url: `${baseUri.accountService}web/admin/coupon/${payload.couponId}`,
    data: { status: payload.newStatus }
  })
  return data
}
export async function updateCouponOfTherapist (payload) {
  const { data } = await $put({
    url: `${baseUri.accountService}web/admin/coupon/${payload.coupon.id}`,
    data: { ...payload.coupon }
  })
  return data
}
export async function delteCouponOfTherapist (payload) {
  const { data } = await $delete({
    url: `${baseUri.accountService}web/admin/coupon/${payload.couponId}`
  })
  return data
}

import {
  $get, baseUri, $download, $delete, $put
} from 'utils/Api'
import { PAGINATION } from 'utils/constants'

export async function getBookingReviewList ({
  bookingId,
  start,
  end,
  page = PAGINATION.PAGE,
  limit = PAGINATION.PER_PAGE
}) {
  const { data } = await $get({
    url: `${baseUri.reviewService}web/admin/review/booking`,
    params: {
      bookingId, start, end, page, limit
    }
  })
  return data
}

export async function exportBookingReviewList ({
  bookingId,
  start,
  end
}) {
  await $download({
    url: `${baseUri.reviewService}web/admin/review/booking/export`,
    params: {
      bookingId, start, end
    },
    data: { filename: 'reviews.csv' }
  })
}

export async function getBookingReviews ({ bookingId }) {
  const { data } = await $get({
    url: `${baseUri.reviewService}web/admin/review/booking/${bookingId}`
  })
  return data
}

export async function getTherapistReviews ({
  userId,
  page = PAGINATION.PAGE,
  limit = PAGINATION.PER_PAGE
}) {
  const { data } = await $get({
    url: `${baseUri.reviewService}web/admin/review/therapist/${userId}`,
    params: { page, limit }
  })
  return data
}

export async function getCustomerReviews ({
  userId,
  page = PAGINATION.PAGE,
  limit = PAGINATION.PER_PAGE
}) {
  const { data } = await $get({
    url: `${baseUri.reviewService}web/admin/review/customer/${userId}`,
    params: { page, limit }
  })
  return data
}

export async function getFeedbacks ({
  page = PAGINATION.PAGE,
  limit = PAGINATION.PER_PAGE
}) {
  const { data } = await $get({
    url: `${baseUri.reviewService}web/admin/feedbacks`,
    params: {
      page, limit
    }
  })
  return data
}

export async function deleteReview (id) {
  const { data } = await $delete({ url: `${baseUri.reviewService}web/admin/review/${id}` })
  return data
}

export async function updateReview (data) {
  const result = await $put({
    url: `${baseUri.reviewService}web/admin/review/${data._id}`,
    data
  })
  return result
}

import {
  $get, $post, $patch, $put, $delete, baseUri
} from 'utils/Api'

const getCouponHeader = () => {
  const headers = {}
  const couponToken = localStorage.getItem('couponToken')
    ? JSON.parse(localStorage.getItem('couponToken'))
    : null
  if (couponToken) {
    headers.Authorization = `Bearer ${couponToken}`
  }
  return headers
}

export async function getCouponList (payload) {
  // eslint-disable-next-line no-param-reassign
  const result = await $get({
    url: `${baseUri.couponService}application/coupons?sort=id&order=DESC`,
    config: {
      headers: getCouponHeader()
    },
    params: payload
  })
  return result
}

export async function createCoupon (payload) {
  const result = await $post({
    url: `${baseUri.couponService}application/coupons`,
    config: {
      headers: getCouponHeader()
    },
    data: payload
  })
  return result
}

export async function getCouponDetail (id) {
  const result = await $get({
    url: `${baseUri.couponService}application/coupons/${id}`,
    config: {
      headers: getCouponHeader()
    }
  })
  return result
}

export async function changeCouponStatus ({ couponId, newStatus }) {
  const result = await $patch({
    url: `${baseUri.couponService}application/coupons/${couponId}/status`,
    config: {
      headers: getCouponHeader()
    },
    data: { status: newStatus }
  })
  return result
}

export async function removeCoupon ({ couponId }) {
  const result = await $delete({
    url: `${baseUri.couponService}application/coupons/${couponId}`,
    config: {
      headers: getCouponHeader()
    }
  })
  return result
}

export async function editCoupon (payload) {
  const result = await $put({
    url: `${baseUri.couponService}application/coupons/${payload.coupon.id}`,
    config: {
      headers: getCouponHeader()
    },
    data: payload
  })
  return result
}

export async function getAnnouncementList (payload) {
  const result = await $get({
    url: `${baseUri.couponService}application/announcements`,
    config: {
      headers: getCouponHeader()
    },
    params: payload
  })
  return result
}

export async function createAnnouncement (payload) {
  const result = await $post({
    url: `${baseUri.couponService}application/announcements`,
    config: {
      headers: getCouponHeader()
    },
    data: payload
  })
  return result
}

export async function editAnnouncement ({ announcementId, announcement }) {
  const result = await $put({
    url: `${baseUri.couponService}application/announcements/${announcementId}`,
    config: {
      headers: getCouponHeader()
    },
    data: announcement
  })
  return result
}

export async function getAnnouncementDetail (id) {
  const result = await $get({
    url: `${baseUri.couponService}application/announcements/${id}`,
    config: {
      headers: getCouponHeader()
    }
  })
  return result
}

export async function changeAnnouncementStatus ({ announcementId, newStatus }) {
  const result = await $put({
    url: `${
      baseUri.couponService
    }application/announcements/${announcementId}/status`,
    config: {
      headers: getCouponHeader()
    },
    data: { status: newStatus }
  })
  return result
}

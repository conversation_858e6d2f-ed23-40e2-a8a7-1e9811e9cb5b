import {
  $get, $patch, $download, baseUri
} from 'utils/Api'

export async function getPayouts ({ payload, status }) {
  const params = payload
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/${status}`,
    params
  })
  return data
}
export async function getPayoutsBySalon ({ payload }) {
  const params = payload
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/salon/payout`,
    params
  })
  return data
}

export async function getPayoutsByTherapist ({ payload, therapistId }) {
  const params = payload
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/therapist/${therapistId}`,
    params
  })
  return data
}
export async function exportPayoutsByTherapist ({ payload, therapistId }) {
  const params = payload
  await $download({
    url: `${baseUri.payoutService}web/admin/payout/therapist/${therapistId}/export`,
    params,
    data: { filename: `payout_of_${therapistId}.csv` }
  })
}

export async function getPayoutIncomes ({ payload, payoutId }) {
  const params = payload
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/${payoutId}/income`,
    params
  })
  return data
}

export async function getEarning (therapistId) {
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/therapist/${therapistId}/earning`
  })
  return data
}

export const markAsPaid = async data => {
  const result = await $patch({
    url: `${baseUri.payoutService}web/admin/payout/status/paid`,
    data
  })
  return result
}
export async function markAsPaidSalonPayout ({ payoutId }) {
  const { data } = await $patch({
    url: `${baseUri.payoutService}/web/admin/salon/payout/${payoutId}/paid`
  })
  return data
}

export const countUnpaid = async () => {
  const result = await $get({
    url: `${baseUri.payoutService}web/admin/payout/unpaid/count`
  })
  return result
}

export const markAllAsPaid = async () => {
  const result = await $patch({
    url: `${baseUri.payoutService}web/admin/payout/all/status/paid`
  })
  return result
}

export const exportPayout = async () => {
  await $download({
    url: `${baseUri.payoutService}web/admin/payout/export`,
    data: { filename: 'payout.csv' }
  })
}

export async function exportSalonPayout (params) {
  await $download({
    url: `${baseUri.payoutService}web/admin/salon/payout/export`,
    data: { filename: params.fileName }
  })
}
export async function exportSalonPayoutDetail (params, payoutId) {
  await $download({
    url: `${baseUri.payoutService}web/admin/salon/payout/${payoutId}/export`,
    data: { filename: params.fileName }
  })
}

export const exportBankTransfer = async () => {
  await $download({
    url: `${baseUri.payoutService}web/admin/payout/bank-transfer/export`,
    data: { filename: 'bank-transfer.csv' }
  })
}

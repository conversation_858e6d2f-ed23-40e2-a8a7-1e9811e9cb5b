import {
  $get, $patch, $download, baseUri
} from 'utils/Api'

export async function getRequestPayment (params) {
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/withdraw`,
    params
  })
  return data
}

export async function getRequestPaymentDetail (id) {
  const { data } = await $get({
    url: `${baseUri.payoutService}web/admin/payout/withdraw/${id}`
  })
  return data
}

export async function updateNote ({ _id, note }) {
  const { data } = await $patch({
    url: `${baseUri.payoutService}web/admin/payout/withdraw/${_id}/note`,
    data: { note: note.trim() }
  })
  return data
}
export async function updateReason ({ _id, reason }) {
  const { data } = await $patch({
    url: `${baseUri.payoutService}web/admin/payout/withdraw/${_id}/reason`,
    data: { reason: reason.trim() }
  })
  return data
}
export async function changeStatus ({
  _id, status, reason, amount
}) {
  const { data } = await $patch({
    url: `${baseUri.payoutService}web/admin/payout/withdraw/${_id}/status`,
    data: { status, reason, amount }
  })
  return data
}

export async function exportRequestPayment (params) {
  await $download({
    url: `${baseUri.payoutService}web/admin/payout/withdraw/export`,
    params,
    data: { filename: 'request-payment.csv' }
  })
}

export async function exportListTransfer (params) {
  await $download({
    url: `${baseUri.payoutService}web/admin/payout/withdraw/export-transfer`,
    params,
    data: { responseType: 'blob', filename: `list_transfer_${new Date().getTime()}.csv` }
  })
}

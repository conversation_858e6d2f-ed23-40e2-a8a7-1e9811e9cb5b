body, h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6, p, .navbar, .brand, .btn-simple, .alert, a, .td-name, td, button.close{
  color: #324452;
  font-family: 'Noto Sans JP', sans-serif !important;
}
.fixed-plugin li > a,
.fixed-plugin .badge{
    transition: all .34s;
    -webkit-transition: all .34s;
    -moz-transition: all .34s;
}

.all-icons [class*="pe-"]{
    font-size: 40px;
}
.all-icons input{
    border: 0;
}
.all-icons .font-icon-detail{
    text-align: center;
    padding: 45px 0px 30px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    margin: 15px 0;
}
.all-icons .font-icon-detail input{
    margin: 25px auto 0;
    width: 100%;
    text-align: center;
    display: block;
    color: #aaa;
    font-size: 13px;
}

#map{
    position:relative;
    width:100%;
    height: calc(100% - 60px);
}

.places-buttons .btn{
    margin-bottom: 30px
}
.sidebar .nav > li.active-pro{
    position: absolute;
    width: 100%;
    bottom: 10px;
}
.sidebar .nav > li.active-pro a{
    background: rgba(255, 255, 255, 0.14);
    opacity: 1;
    color: #FFFFFF;
}

.table-upgrade td:nth-child(2),
.table-upgrade td:nth-child(3){
    text-align: center;
}

.fixed-plugin{
    position: absolute;
    top: 180px;
    right: 0;
    width: 64px;
    background: rgba(0,0,0,.3);
    z-index: 1031;
    border-radius: 8px 0 0 8px;
    text-align: center;
}
.fixed-plugin .fa-cog{
    color: #FFFFFF;
    padding: 10px;
    border-radius: 0 0 6px 6px;
    width: auto;
}
.fixed-plugin .dropdown-menu{
    right: 80px;
    left: auto;
    width: 290px;
    border-radius: 10px;
    padding: 0 10px;
}
.fixed-plugin .dropdown-menu:after, .fixed-plugin .dropdown-menu:before{
    right: 10px;
    margin-left: auto;
    left: auto;
}
.fixed-plugin .fa-circle-thin{
    color: #FFFFFF;
}
.fixed-plugin .active .fa-circle-thin{
    color: #00bbff;
}

.footer-dropdown{
	top: -120px !important;
}

.footer-dropdown:before, .footer-dropdown:after{
	top: 300px !important;
}

.fixed-plugin .dropdown-menu > .active > a,
.fixed-plugin .dropdown-menu > .active > a:hover,
.fixed-plugin .dropdown-menu > .active > a:focus{
    color: #777777;
    text-align: center;
}

.fixed-plugin img{
    border-radius: 0;
    width: 100%;
    max-height: 175px;
    margin: 0 auto;
}

.fixed-plugin .badge{
    border: 3px solid #FFFFFF;
    border-radius: 50%;
    cursor: pointer;
    display: inline-block;
    height: 23px;
    margin-right: 5px;
    position: relative;
    width: 23px;
}
.fixed-plugin .badge.active,
.fixed-plugin .badge:hover{
    border-color: #00bbff;
}

.fixed-plugin .badge-white{
    background-color: #FFFFFF;
}
.fixed-plugin .badge-black{
    background-color: #1DC7EA;
}
.fixed-plugin .badge-azure{
    background-color: #1DC7EA;
}
.fixed-plugin .badge-green{
    background-color: #87CB16;
}
.fixed-plugin .badge-orange{
    background-color: #FFA534;
}
.fixed-plugin .badge-purple{
    background-color: #9368E9;
}
.fixed-plugin .badge-red{
    background-color: #FB404B;
}
.fixed-plugin h5{
    font-size: 14px;
    margin: 10px;
}
.fixed-plugin .dropdown-menu li{
    display: block;
    padding: 5px 2px;
    width: 25%;
    float: left;
}

.fixed-plugin li.adjustments-line,
.fixed-plugin li.header-title,
.fixed-plugin li.button-container{
    width: 100%;
    height: 50px;
    min-height: inherit;
}
.fixed-plugin .pro-title{
    margin: 10px 0 5px 0;
    text-align: center;
}

.fixed-plugin #sharrreTitle{
    text-align: center;
    padding: 10px 0;
    height: 50px;
}

.fixed-plugin li.header-title{
    height: 30px;
    line-height: 40px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.fixed-plugin .adjustments-line p{
    float: left;
    display: inline-block;
    margin-bottom: 0;
    font-size: 1em;
}
.fixed-plugin .adjustments-line .switch{
    float: right;
}
.fixed-plugin .dropdown-menu > li.adjustments-line > a{
      padding-right: 0;
      padding-left: 0;
      border-bottom: 1px solid #ddd;
      margin: 0;
}
.fixed-plugin .dropdown-menu > li > a.img-holder{
      font-size: 16px;
      text-align: center;
      border-radius: 10px;
      background-color: #FFF;
      border: 3px solid #FFF;
      padding-left: 0;
      padding-right: 0;
      opacity: 1;
      cursor: pointer;
      max-height: 86px;
      overflow: hidden;
      padding: 0;
}

.fixed-plugin .dropdown-menu > li > a.switch-trigger:hover,
.fixed-plugin .dropdown-menu > li > a.switch-trigger:focus{
    background-color: transparent;
}
.fixed-plugin .dropdown-menu > li:hover > a.img-holder,
.fixed-plugin .dropdown-menu > li:focus > a.img-holder{
    border-color: rgba(0, 187, 255, 0.53);;
}
.fixed-plugin .dropdown-menu > .active > a.img-holder,
.fixed-plugin .dropdown-menu > .active > a.img-holder{
    border-color: #00bbff;
    background-color: #FFFFFF;
}

.fixed-plugin .dropdown-menu > li > a img{
    margin-top: auto;
}

.fixed-plugin .btn-social{
    width: 50%;
    display: block;
    width: 48%;
    float: left;
    font-weight: 600;
}
.fixed-plugin .btn-social i{
    margin-right: 5px;
}
.fixed-plugin .btn-social:first-child{
    margin-right: 2%;
}

.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus{
    opacity: 1;
}


@media (min-width: 992px){
    .fixed-plugin .dropdown .dropdown-menu{
         -webkit-transform: translateY(-50%);
         -moz-transform: translateY(-50%);
         -o-transform: translateY(-50%);
         -ms-transform: translateY(-50%);
         transform: translateY(-50%);
         top: 27px;
         opacity: 0;

         transform-origin: 0 0;
    }
    .fixed-plugin .dropdown.open .dropdown-menu{
         opacity: 1;

         -webkit-transform: translateY(-50%);
         -moz-transform: translateY(-50%);
         -o-transform: translateY(-50%);
         -ms-transform: translateY(-50%);
         transform: translateY(-50%);

         transform-origin: 0 0;
    }

    .fixed-plugin .dropdown-menu:before,
    .fixed-plugin .dropdown-menu:after{
        content: "";
        display: inline-block;
        position: absolute;
        top: 50%;
        width: 16px;
        transform: translateY(-50%);
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);

    }
    .fixed-plugin .dropdown-menu:before{
        border-bottom: 16px solid rgba(0, 0, 0, 0);
        border-left: 16px solid rgba(0,0,0,0.2);
        border-top: 16px solid rgba(0,0,0,0);
        right: -16px;
    }

    .fixed-plugin .dropdown-menu:after{
        border-bottom: 16px solid rgba(0, 0, 0, 0);
        border-left: 16px solid #FFFFFF;
        border-top: 16px solid rgba(0,0,0,0);
        right: -15px;
    }

    .typo-line{
        padding-left: 140px;
        margin-bottom: 40px;
        position: relative;
    }

    .typo-line .category{
        transform: translateY(-50%);
        top: 50%;
        left: 0px;
        position: absolute;
    }

    .fixed-plugin{
        top: 300px;
    }

}

@media (max-width: 991px){
    .fixed-plugin .dropdown-menu{
        right: 60px;
        width: 220px;
    }
    .fixed-plugin .dropdown-menu li{
        width: 50%;
    }

    .fixed-plugin li.adjustments-line,
    .fixed-plugin li.header-title,
    .fixed-plugin li.button-container{
        width: 100%;
        height: 55px;
        min-height: inherit;
    }

    .fixed-plugin .adjustments-line .switch{
        float: right;
        margin: 0 0px;
    }

    .fixed-plugin li.header-title{
        height: 40px;
    }
    .fixed-plugin .dropdown .dropdown-menu{
        top: -170px;
    }
    #container-notification{
      display: none;
    }
    .right-menu.nav .dropdown{
      margin: 0px !important;
      background: #fad390;
    }
    .right-menu.nav .dropdown a{
      margin: 0px !important;
      border-radius: 0px !important;
    }
    .right-menu.nav .dropdown > ul > li > a:hover{
      background-color: transparent !important;
    }
    .sidebar .nav .caret {
      top: 32px !important;
    }
}

.btn-social {
  opacity: 0.85;
  padding: 8px 9px;
}
.btn-social .fa {
  font-size: 18px;
  vertical-align: middle;
  display: inline-block;
}
.btn-social.btn-round {
  padding: 9px 10px;
}
.btn-social.btn-simple {
  padding: 9px 5px;
  font-size: 16px;
}
.btn-social.btn-simple .fa {
  font-size: 20px;
  position: relative;
  top: -2px;
  width: 24px;
}

.btn-facebook {
  border-color: #3b5998;
  color: #3b5998;
}
.btn-facebook:hover,
.btn-facebook:focus,
.btn-facebook:active,
.btn-facebook.active,
.open > .btn-facebook.dropdown-toggle {
  background-color: transparent;
  color: #3b5998;
  border-color: #3b5998;
  opacity: 1;
}
.btn-facebook:disabled,
.btn-facebook[disabled],
.btn-facebook.disabled {
  background-color: transparent;
  border-color: #3b5998;
}
.btn-facebook.btn-fill {
  color: #ffffff;
  background-color: #3b5998;
  opacity: 0.9;
}
.btn-facebook.btn-fill:hover,
.btn-facebook.btn-fill:focus,
.btn-facebook.btn-fill:active,
.btn-facebook.btn-fill.active,
.open > .btn-facebook.btn-fill.dropdown-toggle {
  background-color: #3b5998;
  color: #ffffff;
  opacity: 1;
}
.btn-twitter {
  border-color: #55acee;
  color: #55acee;
}
.btn-twitter:hover,
.btn-twitter:focus,
.btn-twitter:active,
.btn-twitter.active,
.open > .btn-twitter.dropdown-toggle {
  background-color: transparent;
  color: #55acee;
  border-color: #55acee;
  opacity: 1;
}
.btn-twitter:disabled,
.btn-twitter[disabled],
.btn-twitter.disabled {
  background-color: transparent;
  border-color: #55acee;
}
.btn-twitter.btn-fill {
  color: #ffffff;
  background-color: #55acee;
  opacity: 0.9;
}
.btn-twitter.btn-fill:hover,
.btn-twitter.btn-fill:focus,
.btn-twitter.btn-fill:active,
.btn-twitter.btn-fill.active,
.open > .btn-twitter.btn-fill.dropdown-toggle {
  background-color: #55acee;
  color: #ffffff;
  opacity: 1;
}

@media (min-width: 992px){
    .typo-line{
        padding-left: 140px;
        margin-bottom: 40px;
        position: relative;
    }

    .typo-line .category{
        transform: translateY(-50%);
        top: 50%;
        left: 0px;
        position: absolute;
    }
}

.all-icons [class*="pe-"]{
    font-size: 40px;
}
.all-icons input{
    border: 0;
}
.all-icons .font-icon-detail{
    text-align: center;
    padding: 45px 0px 30px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    margin: 15px 0;
}
.all-icons .font-icon-detail input{
    margin: 25px auto 0;
    width: 100%;
    text-align: center;
    display: block;
    color: #aaa;
    font-size: 13px;
}

#map{
    position:relative;
    width:100%;
    height: calc(100% - 60px);
}

.places-buttons .btn{
    margin-bottom: 30px
}
.sidebar .nav > li.active-pro{
    position: absolute;
    width: 100%;
    bottom: 10px;
}
.sidebar .nav > li.active-pro a{
    background: rgba(255, 255, 255, 0.14);
    opacity: 1;
    color: #FFFFFF;
}

.table-upgrade td:nth-child(2),
.table-upgrade td:nth-child(3){
    text-align: center;
}
.container-box-full-page {
  background: #ffffff;
  padding: 30px 30px 20px;
  border-radius: 2px;
  max-width: 400px;
  margin: 0px auto;
}
#container-login{
  position: fixed;
  top: 0px;
  left: 0px;
  background: #424f66;
  color: #353c4e;
  width: 100vw;
  height: 100vh;
  padding: 100px 0px 0px 0px;
  z-index: 100;
}
.has-feedback{
  position: relative;
}
.form-control.has-feedback-left {
  padding-left: 45px;
}
.form-control-feedback {
  margin-top: 8px;
  height: 23px;
  color: #bbb;
  line-height: 24px;
  font-size: 21px !important;
  position: absolute;
  left: 13px;
  top: 15px !important;
}
.full-width{
  width: 100%;
}
.right-menu li:last-child{
  margin-right: 5px;
}
.box-item{
  overflow: hidden;
}
.btn-booking {
  color: #fff !important;
  background-color: #FFD280 !important;
  border-color: #FFD280 !important;
}
.btn-step{
  border-radius: 4px;
  box-shadow: 0 4px 8px 0 rgba(96, 128, 153, 0.2);
  border: solid 1px #608099;
  background-color: #fff;
}
.btn-step:focus{
  outline:0;
}
.btn-item-group{
  border: solid 1px #758ea3;
  padding: 5px 35px;
}
.btn-item-group:hover{
  background: #fff;
  border: solid 1px #758ea3;
}
.btn-item-group:focus{
  outline:0;
}
.btn-item-group.active-item{
  border: solid 1px #758ea3;
  background: #758ea3;
  color: #fff;
}

.react-datepicker-wrapper, .react-datepicker__input-container{
  display: block;
  width: 100%;
}
html {font-size: 14px;}
.confirm-popup.modal{
  z-index: 2000 !important;
}
.confirm-popup.confirm-overflow{
  z-index: 2000;
}
.confirm-popup.modal-dialog {
  padding-top: 15%;
}
.confirm-popup .modal-body{
  padding: 20px !important;
}
.confirm-popup .modal-body > img {
  margin-right: 20px;
}
.sidebar{
  background-image: linear-gradient(13deg, #ffd280, #ffdb9a 28%, #ffe6b8 50%, #fff1d6 75%, #fff3dc);
}
#main-panel{
  background-image: linear-gradient(54deg, #fff5e4, #fffbf4 28%, #fefdfa 50%, #f7fbfd 75%, #e2eef9);
  min-height: 100vh;
}
.main-panel .navbar{
  background: transparent;
}
.table-list{
  border-collapse:separate;
  border-spacing:0 8px;
}
.table-list > tbody > tr{
  border-radius: 8px;
  box-shadow: 0 0 10px 0 rgba(96, 128, 153, 0.1);
  background-color: #fff;
  cursor: pointer;
}
.table-list.no-pointer > tbody > tr{
  cursor: default;
}
.table-list > tbody > tr:hover{
  box-shadow: 0 8px 16px 0 rgba(255, 210, 128, 0.56);
  background-color: #ffd280;
  font-weight: bold;
}
.table-list > tbody > tr.active-row{
  box-shadow: 0 8px 16px 0 rgba(255, 210, 128, 0.56);
  background-color: #ffd280;
}
.table-list > tbody > tr > td {
  border: 0px;
  height: 64px;
}
.table-list > tbody > tr:hover > td,
.table-list > tbody > tr.active-row > td {
  font-weight: bold;
}
.table-list > thead > tr > th {
  border: 0px;
  white-space: nowrap;
}
.table-list td:last-child {
  border-right-width: 3px;
  border-radius: 0 5px 5px 0;
}
.table-list td:first-child {
  border-left-width: 3px;
  border-radius: 5px 0px 0px 5px;
}
.table-list > thead > tr > th{
  font-size: 12px;
  font-weight: bold;
  color: #324452;
  padding-bottom: 8px;
}
.btn-close{
  border-radius: 4px;
  box-shadow: 0 4px 8px 0 rgba(96, 128, 153, 0.2);
  border: solid 1px #608099;
  background-color: #fff;
  color:#608099;
  padding: 8px 20px !important;
  font-size: 16px;
  font-weight: 500;
}
.btn-close:hover{
  color: #608099 !important;
  background-color: #fff !important;
}
.btn-close:focus {
  outline:0;
  color: #608099 !important;
  background-image: #fff;
}
.btn-blue{
  border-radius: 4px;
  box-shadow: 0 4px 8px 0 rgba(96, 128, 153, 0.4) !important;
  background-image: linear-gradient(76deg, #608099, #5894c7) !important;
  color: #fff !important;
  border: 0px !important;
  padding: 10px 20px;
}
.btn-blue:hover{
  color: #fff !important;
}
.btn-blue:focus {
  outline:0;
  color: #fff !important;
  background-image: linear-gradient(76deg, #608099, #5894c7);
}
#page-title{
  font-size: 28px;
  font-weight: 500;
  color: #324452;
  padding: 10px 0px 0px 27px;
}
.new-modal .modal-body{
  padding-top: 0px;
}
.new-modal .modal-header{
  background-image: linear-gradient(85deg, rgba(255, 210, 128, 0.2), rgba(255, 240, 212, 0.2) 28%, rgba(255, 246, 229, 0.2) 50%, rgba(204, 233, 255, 0.2) 75%, rgba(107, 170, 220, 0.2));
  padding: 24px;
}
.new-modal .modal-header .close{
  margin-top: 0px;
  opacity: 1;
  text-shadow:none;
}
.small-margin-left{
  margin-left: 10px;
}
.overme {
  overflow:hidden;
  white-space:nowrap;
  text-overflow: ellipsis;
}
.form-control{
  border-color: #a7b5c0;
  color: #324452;
}
.form-group label{
  font-weight: 500;
  color: #758ea3
}
.input-group-addon {
  border-color: #a7b5c0;
  background-color: #a7b5c026;
}
.explain-text{
  font-size: 12px;
  float: right;
  color: #a7b5c0
}
.clearPadding{
  padding: 0px;
}
.modal-large-fix{
  width: 980px;
}
.avatar-in-list{
  margin: 0 4px 0 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
.sidebar .nav li > a{
  padding: 20px 24px;
}
.sidebar::before{
  background-color: transparent !important;
}
.navbar-toggle{
  float: left !important;
}
#app{
  overflow: hidden;
}
.modal-header{
  background-image: linear-gradient(85deg, rgba(255, 210, 128, 0.2), rgba(255, 240, 212, 0.2) 28%, rgba(255, 246, 229, 0.2) 50%, rgba(204, 233, 255, 0.2) 75%, rgba(107, 170, 220, 0.2));
  padding: 24px;
}
.canceling-group-radio{
  font-size: 12px;
}
.canceling-group-radio input[type="radio"]{
  position: relative;
  top: 2px
}
.extensions-title{
  text-align: center;
  border-top: 1px dashed #ddd ;
}
.extensions-title > span{
  background: #f7fbfd;
  top: -12px;
  position: relative;
  padding: 0px 20px;
  color:#a7b5c0
}
.clear-border td{
  border-width: 0px !important
}
button.close{
  opacity: 1 !important
}
.menu--icon {
  background-color: #e4ebef;
}
.ml-2 {
  margin-left: 8px;
}
.mr-2 {
  margin-right: 0.6rem;
}
.badge {
  border-radius: 4px;
  color: #fff;
  font-size: 12px;
}
.badge--secondary {
  background: #a7b5c0;
}
.badge--primary {
  background: #608099;
}
.badge--warning {
  background: #FFD280;
}
.badge--light {
  background: white;
  border: 1px solid #a7b5c0;
  color: #324452;
}
.media {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
}
.media .thumbnail {
  width: 40px;
  height: 40px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  display: inline-block;
}
.media .thumbnail--round {
  border-radius: 50%;
}
.media-body {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: block;
}
.card {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0,0,0,.125);
  border-radius: .25rem;
}
.card-header {
  padding: .75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0,0,0,.03);
  border-bottom: 1px solid rgba(0,0,0,.125);
}
.card-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1.25rem;
}
.btn-light {
  border: 1px solid dodgerblue;
  color: dodgerblue;
  background: white;
  white-space: nowrap;
  padding: 4px 16px;
}

@media (min-width: 768px) {
  .modal-dialog.modal-md {
    width: 820px;
  }
}

.btn-remove {
  border: 1px solid #ef5350;
  color: #ef5350;
  background: #fff0f0;
  white-space: nowrap;
  padding: 6px 16px;
}
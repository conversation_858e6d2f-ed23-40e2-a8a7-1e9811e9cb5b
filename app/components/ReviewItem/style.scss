.ReviewItem {
  background-color: #ebeef0;
  padding: 16px;
  border-radius: 4px;
  position: relative;

  .thumbnail {
    width: 40px;
    height: 40px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0;
  }

  // Customer layout (horizontal)
  &__layout-customer {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .ReviewItem__left {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      flex: 1;
    }

    .ReviewItem__content-wrapper {
      flex: 1;
    }

    .ReviewItem__name-rating {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
    }

    .ReviewItem__name-section {
      margin-bottom: 8px;
    }

         .ReviewItem__rating {
       display: flex;
       gap: 4px;
       font-size: 14px;

       i {
         &.fa-star {
           color: #ffc107;
         }

         &.fa-star-o {
           color: #ffc107;
           -webkit-text-stroke: 0.5px #ffc107;
         }
       }
     }
  }

  // Table-based review content styling
  &__table-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px 0;

    &--customer {
      .ReviewItem__table-rating {
       
        .star-rating {
          display: flex;
          gap: 2px;

          i {
            font-size: 16px;
            
            &.fa-star {
              color: #FFB300;
            }

            &.fa-star-o {
              color: #FFB300;
            }
          }
        }
      }

      .ReviewItem__table-subcategories {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 12px;

        .subcategory-item {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 4px;

          .subcategory-label {
            font-family: 'Noto Sans JP', sans-serif;
            font-weight: 400;
            font-size: 12px;
            line-height: 1.33;
            color: #A7B5C0;
          }

          .category-icon {
            width: 16px;
            height: 16px;
          }
        }
      }

      .ReviewItem__table-comment {
       
        .comment-text {
          font-family: 'Noto Sans JP', sans-serif;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.43;
          color: #324452;
        }
      }
    }

    &--therapist {
      .ReviewItem__table-rating {
        .emoji-rating {
          font-size: 16px;
          
          span[role="img"] {
            font-size: 16px;
          }
        }
      }

      .ReviewItem__table-comment {
      
        .comment-text {
          font-family: 'Noto Sans JP', sans-serif;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.43;
          color: #324452;
        }
      }
    }
  }

  // Therapist layout (avatar separate, then content)
  &__layout-therapist {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .ReviewItem__therapist-content {
      width: 448px;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .ReviewItem__therapist-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
    }

    .ReviewItem__therapist-user {
      width: 194px;
    }

    .ReviewItem__therapist-rating {
      padding: 4px 0 0;
    }

    .ReviewItem__therapist-comment {
      width: 100%;
      margin-top: 4px;
    }
  }

  // Name and tag section styling
  &__name-tag {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__name-time {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .name-reviewer {
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.43;
    color: #324452;
  }

  .review-date {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 2px 0 1px;

    .date-bullet {
      font-family: 'Noto Sans JP', sans-serif;
      font-weight: 400;
      font-size: 12px;
      line-height: 1.33;
      color: #5f6e7a;
    }

    .date-text {
      font-family: 'Noto Sans JP', sans-serif;
      font-weight: 400;
      font-size: 12px;
      line-height: 1.33;
      color: #5f6e7a;
    }
  }

  &__tags-group {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .badge {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 1px 8px 2px;
    height: 20px;
    border: 1px solid #c1c9d1;
    border-radius: 4px;
    background-color: #ffffff;
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.45;
    color: #324452;
    text-decoration: none;

    &:hover {
      text-decoration: none;
      color: #324452;
    }
  }

  .badge-role,
  .badge-booking {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    padding: 1px 8px 2px;
    height: 20px;
    border: 1px solid #c1c9d1;
    border-radius: 4px;
    background-color: #ffffff;
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.45;
    color: #324452;
    text-decoration: none;

    &:hover {
      text-decoration: none;
      color: #324452;
    }
  }

  // Action buttons
  &__action {
    display: flex;
    gap: 8px;

    .btn {
      width: 24px;
      height: 24px;
      background: #fff;
      border: 1px solid #DAE1E7;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 4px;
      cursor: pointer;

      &:hover {
        background: #f5f5f5;
      }

      i {
        font-size: 16px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.fa-pencil {
          color: #608099;
        }

        &.fa-trash {
          color: #888888;
        }
      }
    }
  }

  // Categories section
  &__categories {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;

    .category-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .category-label {
        font-size: 14px;
        color: #324452;
        font-weight: 500;
      }

      .category-icon {
        width: 24px;
        height: 24px;
      }
    }
  }

  // Comment section
  &__comment {
    font-size: 14px;
    color: #324452;
    line-height: 1.5;
    margin-top: 8px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }


  &__emoji-counts {
    display: flex;
    justify-content: center;
    gap: 64px;
    padding: 16px 0;

    .emoji-count-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 6px 0;
      width: 40px;

      .emoji-icon {
        width: 40px;
        height: 40px;
      }

      .emoji-count {
        font-family: 'Noto Sans JP', sans-serif;
        font-weight: 500;
        font-size: 14px;
        line-height: 1.43;
        text-align: center;
        color: #324452;
        width: 48px;
      }
    }
  }


  &__category-ratings {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 6px 0;
    width: 216px;
    margin-left: 80px; // Align with the design layout

    .category-labels,
    .category-emojis,
    .category-counts,
    .category-counts-secondary {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 0;
      gap: 8px;
    }

    .category-labels {
      .category-label {
        font-family: 'Noto Sans JP', sans-serif;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.43;
        text-align: center;
        color: #5f6e7a;
        width: 48px;
      }
    }

    .category-emojis {
      justify-content: space-between;
      
      .category-icon {
        width: 20px;
        height: 20px;
      }
    }

    .category-counts,
    .category-counts-secondary {
      .category-count {
        font-family: 'Noto Sans JP', sans-serif;
        font-weight: 500;
        font-size: 14px;
        line-height: 1.43;
        text-align: center;
        color: #324452;
        width: 48px;
      }
    }
  }

  // Table-based review content styling
  &__table-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px 0;

    &--customer {
      .ReviewItem__table-rating {
        
        .star-rating {
          display: flex;
          gap: 2px;

          i {
            font-size: 16px;
            
            &.fa-star {
              color: #FFB300;
            }

            &.fa-star-o {
              color: #FFB300;
            }
          }
        }
      }

      .ReviewItem__table-subcategories {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 12px;

        .subcategory-item {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 4px;

          .subcategory-label {
            font-family: 'Noto Sans JP', sans-serif;
            font-weight: 400;
            font-size: 12px;
            line-height: 1.33;
            color: #A7B5C0;
          }

          .category-icon {
            width: 16px;
            height: 16px;
          }
        }
      }

      .ReviewItem__table-comment {
        
        .comment-text {
          font-family: 'Noto Sans JP', sans-serif;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.43;
          color: #324452;
        }
      }
    }

    &--therapist {
      .ReviewItem__table-rating {
       
        .emoji-rating {
          font-size: 16px;
          
          span[role="img"] {
            font-size: 16px;
          }
        }
      }

      .ReviewItem__table-comment {

        .comment-text {
          font-family: 'Noto Sans JP', sans-serif;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.43;
          color: #324452;
        }
      }
    }
  }
}


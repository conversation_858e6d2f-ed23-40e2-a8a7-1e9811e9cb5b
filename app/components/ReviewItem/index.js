import React, { Fragment, useState } from 'react'
import PropTypes from 'prop-types'
import { toast } from 'react-toastify'
import { injectIntl, intlShape, FormattedMessage } from 'react-intl'
import { Media } from 'react-bootstrap'
import moment from 'moment-timezone'
import _ from 'lodash'
import defaultProfile from 'assets/img/default-profile.png'
import globalMessages from 'utils/globalMessages'
import { deleteBookingReviews, getBookingReviews, updateBookingReviews } from 'containers/Review/actions'
import Confirm from 'components/Confirm'
import ReviewForm from 'components/ReviewForm'
import { makeSelectCurrentUserData } from 'containers/Account/selectors'
import { ROLES } from 'utils/constants'
import './style.scss'
import { compose } from 'redux'
import { createStructuredSelector } from 'reselect'
import { connect } from 'react-redux'
import messages from './messages'

const PUBLIC_PATH = process.env.publicPath || '/'

const ReviewItem = ({
  review, user = {}, account, intl, type, deleteReview, getReviews, updateReview
}) => {
  const isAgent = account.role === ROLES.AGENT
  const [isShowEdit, setIsShowEdit] = useState(false)
  const [confirm, setConfirm] = useState(false)
  const removeReview = async () => {
    setConfirm(false)
    try {
      await deleteReview(review)
    } catch (e) {
      toast.error(e.message)
    }
    getReviews(review.bookingId)
  }

  const editReview = async values => {
    const formValues = values.toJS()
    const { rating, categories, overall } = formValues
    const { role } = user
    let payload
    if (role === 'customer') {
      payload = {
        role,
        rating,
        categories: {
          technique: categories.technique ? categories.technique : review.categories.technique,
          service: categories.service ? categories.service : review.categories.service,
          cost: categories.cost ? categories.cost : review.categories.cost
        },
        comment: {
          overall
        }
      }
    } else {
      payload = {
        role,
        rating: 5,
        categories: {
          overall: categories.therapistImpression
        },
        comment: {
          overall
        }
      }
    }

    const data = {
      _id: review._id,
      ...payload
    }

    setIsShowEdit(false)
    try {
      await updateReview(data)
    } catch (e) {
      toast.error(e.message)
    }
    getReviews(review.bookingId)
  }
  return (
    <React.Fragment>
      <Media className='ReviewItem'>
        <figure
          className='thumbnail thumbnail--round mr-2'
          style={{
            backgroundImage: `url("${_.get(user, 'avatar') ||
            _.get(user, 'profilePicture.url') ||
            defaultProfile}")`
          }}
        />
        <Media.Body>
          <div className='ReviewItem__header'>
            <div className='ReviewItem__user'>
              <span>
                <div className='name-reviewer'>
                  {_.truncate((user.name || user.fullName || intl.formatMessage(globalMessages.anonymous)), { length: 20 })}
                </div>
                {user.role && (
                  ((!isAgent || user.role !== ROLES.CUSTOMER) && (user.id || user._id)) ? (
                    <a href={`${PUBLIC_PATH}${user.role}/${user.id || user._id}`} target='_blank'>
                      <span className='badge badge--light'>
                        {intl.formatMessage(globalMessages[`role.${user.role}`])}
                      </span>
                    </a>
                  ) : (
                    <span className='badge badge--light'>
                      #
                      {review.bookingId}
                    </span>
                  )
                )}
                {_.get(review, 'bookingId') && (
                  type === 'ReviewTab'
                    ? (
                      <a href={`${PUBLIC_PATH}booking/${review.bookingId}`} target='_blank'>
                        <span className='badge badge--light'>
                          #
                          {review.bookingId}
                        </span>
                      </a>
                    )
                    : (
                      <span className='badge badge--light'>
                        #
                        {review.bookingId}
                      </span>
                    )
                )}
              </span>
              {_.get(review, 'createdAt') && (
                <span className='ReviewItem__date'>
                  {moment(review.createdAt).format('YYYY/MM/DD')}
                </span>
              )}
              {
                !isAgent && _.get(review, '_id') && type === 'ReviewModal' && (
                  <span className='ReviewItem__group-action'>
                    <i className='fa fa-pencil text-primary btn-default btn badge badge--light' onClick={() => { setIsShowEdit(true) }} />
                    <i className='fa fa-trash text-danger btn-default btn badge badge--light' onClick={() => { setConfirm(true) }} />
                  </span>
                )
              }
            </div>
            {_.get(review, '_id') ? (
              <div className='ReviewItem__rating'>
                {Array(review.rating)
                  .fill(review.rating)
                  .map(i => (
                    <i className='fa fa-star' />
                  ))}
                {Array(5 - review.rating)
                  .fill(5 - review.rating)
                  .map(i => (
                    <i className='fa fa-star-o' />
                  ))}
              </div>
            ) : (
              <p>
                <FormattedMessage {...messages.notReview} />
              </p>
            )}
          </div>
          {_.get(review, '_id') && (
            <div className='ReviewItem__content'>
              {user.role === 'therapist' ? (
                <dl>
                  {_.get(review, 'comment.booking') && (
                    <Fragment>
                      <dt>
                        <FormattedMessage {...messages.booking} />
                      </dt>
                      <dd>
                        {_.get(review, 'comment.booking')}
                      </dd>
                    </Fragment>
                  )}
                  {_.get(review, 'comment.place') && (
                    <Fragment>
                      <dt>
                        <FormattedMessage {...messages.place} />
                      </dt>
                      <dd>
                        {_.get(review, 'comment.place')}
                      </dd>
                    </Fragment>
                  )}
                </dl>
              ) : _.get(review, 'comment.therapist')}
            </div>
          )}
        </Media.Body>
      </Media>
      <Confirm
        title={<FormattedMessage {...messages.confirmTitle} />}
        content={(
          <FormattedMessage
            {...messages.confirmContent}
          />
        )}
        show={confirm}
        handleHide={() => { setConfirm(false) }}
        handleAction={() => { removeReview() }}
        closeButton={<FormattedMessage {...messages.cancel} />}
        okButton={<FormattedMessage {...messages.ok} />}
        isShowIcon={false}
      />
      <ReviewForm
        key={review._id}
        onSubmit={values => { editReview(values) }}
        user={user}
        review={review}
        show={isShowEdit}
        onHide={() => { setIsShowEdit(false) }}
      />

    </React.Fragment>
  )
}

ReviewItem.propTypes = {
  intl: intlShape.isRequired,
  review: PropTypes.object,
  user: PropTypes.object,
  account: PropTypes.object,
  type: PropTypes.string,
  deleteReview: PropTypes.func,
  updateReview: PropTypes.func,
  getReviews: PropTypes.func
}
const mapStateToProps = createStructuredSelector({
  account: makeSelectCurrentUserData()
})

const mapDispatchToProps = dispatch => ({
  deleteReview: review => dispatch(deleteBookingReviews(review)),
  updateReview: review => dispatch(updateBookingReviews(review)),
  getReviews: bookingId => dispatch(getBookingReviews(bookingId))
})

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
)

export default compose(
  withConnect,
  injectIntl
)(ReviewItem)

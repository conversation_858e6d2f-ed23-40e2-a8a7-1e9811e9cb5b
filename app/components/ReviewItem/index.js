import React, { Fragment, useState } from 'react'
import PropTypes from 'prop-types'
import { toast } from 'react-toastify'
import { injectIntl, intlShape, FormattedMessage } from 'react-intl'
import _ from 'lodash'
import moment from 'moment-timezone'
import defaultProfile from 'assets/img/default-profile.png'
import ratingHappyActive from 'assets/img/rating-happy-active.svg'
import ratingSadActive from 'assets/img/rating-sad-active.svg'
import therapistRatingHappyActive from 'assets/img/therapist-rating-happy-active.svg'
import therapistRatingSadActive from 'assets/img/therapist-rating-sad-active.svg'
import globalMessages from 'utils/globalMessages'
import { deleteBookingReviews, getBookingReviews, updateBookingReviews } from 'containers/Review/actions'
import Confirm from 'components/Confirm'
import ReviewForm from 'components/ReviewForm'
import { makeSelectCurrentUserData } from 'containers/Account/selectors'
import { ROLES } from 'utils/constants'
import './style.scss'
import { compose } from 'redux'
import { createStructuredSelector } from 'reselect'
import { connect } from 'react-redux'
import messages from './messages'

const PUBLIC_PATH = process.env.publicPath || '/'

const ReviewItem = ({
  review, user = {}, account, intl, type, deleteReview, getReviews, updateReview
}) => {
  const isAgent = account.role === ROLES.AGENT
  const [isShowEdit, setIsShowEdit] = useState(false)
  const [confirm, setConfirm] = useState(false)
  const removeReview = async () => {
    setConfirm(false)
    try {
      await deleteReview(review)
    } catch (e) {
      toast.error(e.message)
    }
    getReviews(review.bookingId)
  }

  const editReview = async values => {
    const formValues = values.toJS()
    const { rating, categories, overall } = formValues
    const { role } = user
    let payload
    if (role === 'customer') {
      payload = {
        role,
        rating,
        categories: {
          technique: categories.technique ? categories.technique : review.categories.technique,
          service: categories.service ? categories.service : review.categories.service,
          cost: categories.cost ? categories.cost : review.categories.cost
        },
        comment: {
          overall
        }
      }
    } else {
      // For therapist: check if user modified the category rating, otherwise use existing value
      const categoryValue = categories.therapistImpression !== undefined
        ? categories.therapistImpression
        : review.categories.overall
      payload = {
        role,
        rating: 5,
        categories: {
          overall: categoryValue
        },
        comment: {
          overall
        }
      }
    }

    const data = {
      _id: review._id,
      ...payload
    }

    setIsShowEdit(false)
    try {
      await updateReview(data)
    } catch (e) {
      toast.error(e.message)
    }
    getReviews(review.bookingId)
  }

  const getCategoryIcon = (categoryValue, userRole) => {
    if (categoryValue === 'GOOD') {
      const icon = userRole === 'therapist' ? therapistRatingHappyActive : ratingHappyActive
      return <img src={icon} alt='Good' className='category-icon' />
    }
    if (categoryValue === 'BAD') {
      const icon = userRole === 'therapist' ? therapistRatingSadActive : ratingSadActive
      return <img src={icon} alt='Bad' className='category-icon' />
    }
    return null
  }

  const renderCategoryRatings = () => {
    const categories = _.get(review, 'categories', {})

    if (user.role === 'customer') {
      return (
        <div className='ReviewItem__categories'>
          <div className='category-item'>
            <span className='category-label'>技術</span>
            {getCategoryIcon(categories.technique, user.role)}
          </div>
          <div className='category-item'>
            <span className='category-label'>接客</span>
            {getCategoryIcon(categories.service, user.role)}
          </div>
          <div className='category-item'>
            <span className='category-label'>コスパ</span>
            {getCategoryIcon(categories.cost, user.role)}
          </div>
        </div>
      )
    }

    // For therapist reviews, show overall category without label
    if (user.role === 'therapist' && categories.overall) {
      return (
        <div className='ReviewItem__categories'>
          <div className='category-item'>
            {getCategoryIcon(categories.overall, user.role)}
          </div>
        </div>
      )
    }

    return null
  }

  const renderUserSection = () => {
    const userName = user.name || user.fullName || intl.formatMessage(globalMessages.anonymous)
    const reviewDate = _.get(review, 'createdAt') ? moment(review.createdAt).format('YYYY/MM/DD') : null

    return (
      <div className='ReviewItem__name-tag'>
        {/* Name + Time Row */}
        <div className='ReviewItem__name-time'>
          <div className='name-reviewer'>
            {_.truncate(userName, { length: 20 })}
          </div>
          {reviewDate && (
            <div className='review-date'>
              <span className='date-bullet'>·</span>
              <span className='date-text'>{reviewDate}</span>
            </div>
          )}
        </div>

        {/* Tags Row */}
        <div className='ReviewItem__tags-group'>
          {user.role && (
            <span className='badge badge-role'>
              {intl.formatMessage(globalMessages[`role.${user.role}`])}
            </span>
          )}
          {_.get(review, 'bookingId') && (
            (type === 'ReviewTab' && (user.id || user._id)) ? (
              <a href={`${PUBLIC_PATH}booking/${review.bookingId}`} target='_blank' rel='noopener noreferrer'>
                <span className='badge badge-booking'>
                  #
                  {review.bookingId}
                </span>
              </a>
            ) : (
              <span className='badge badge-booking'>
                #
                {review.bookingId}
              </span>
            )
          )}
        </div>
      </div>
    )
  }

  const renderContent = () => {
    if (!_.get(review, '_id')) {
      return (
        <p>
          <FormattedMessage {...messages.notReview} />
        </p>
      )
    }

    return (
      <div className='ReviewItem__content'>
        {_.get(review, 'categories') && renderCategoryRatings()}
        <div className='ReviewItem__comment'>
          {user.role === 'therapist'
            ? (_.get(review, 'comment.overall') || _.get(review, 'comment.booking') || _.get(review, 'comment.place'))
            : (_.get(review, 'comment.overall') || _.get(review, 'comment.therapist'))
          }
        </div>
      </div>
    )
  }

  return (
    <React.Fragment>
      <div className='ReviewItem'>
        {/* For customer reviews, follow the first layout */}
        {user.role === 'customer' ? (
          <div className='ReviewItem__layout-customer'>
            <div className='ReviewItem__left'>
              <figure
                className='thumbnail'
                style={{
                  backgroundImage: `url("${_.get(user, 'avatar') ||
                  _.get(user, 'profilePicture.url') ||
                  defaultProfile}")`
                }}
              />
              <div className='ReviewItem__content-wrapper'>
                <div className='ReviewItem__name-rating'>
                  {renderUserSection()}
                  {_.get(review, '_id') && (
                    <div className='ReviewItem__rating'>
                      {Array(review.rating)
                        .fill(review.rating)
                        .map((item, index) => (
                          <i key={index} className='fa fa-star' />
                        ))}
                      {Array(5 - review.rating)
                        .fill(5 - review.rating)
                        .map((item, index) => (
                          <i key={index} className='fa fa-star-o' />
                        ))}
                    </div>
                  )}
                </div>
                {renderContent()}
              </div>
            </div>
            <div className='ReviewItem__action'>
              {!isAgent && _.get(review, '_id') && type === 'ReviewModal' && (
                <>
                  <button type='button' className='btn' onClick={() => { setIsShowEdit(true) }}>
                    <i className='fa fa-pencil text-primary' />
                  </button>
                  <button type='button' className='btn' onClick={() => { setConfirm(true) }}>
                    <i className='fa fa-trash text-danger' />
                  </button>
                </>
              )}
            </div>
          </div>
        ) : (
          /* For therapist reviews, follow the second layout per Figma */
          <div className='ReviewItem__layout-therapist'>
            <figure
              className='thumbnail'
              style={{
                backgroundImage: `url("${_.get(user, 'avatar') ||
                _.get(user, 'profilePicture.url') ||
                defaultProfile}")`
              }}
            />
            <div className='ReviewItem__therapist-content'>
              <div className='ReviewItem__therapist-header'>
                <div className='ReviewItem__therapist-user'>
                  {renderUserSection()}
                </div>
                <div className='ReviewItem__action'>
                  {!isAgent && _.get(review, '_id') && type === 'ReviewModal' && (
                    <>
                      <button type='button' className='btn' onClick={() => { setIsShowEdit(true) }}>
                        <i className='fa fa-pencil text-primary' />
                      </button>
                      <button type='button' className='btn' onClick={() => { setConfirm(true) }}>
                        <i className='fa fa-trash text-danger' />
                      </button>
                    </>
                  )}
                </div>
              </div>
              {/* Category rating section for therapist */}
              {_.get(review, 'categories') && (
                <div className='ReviewItem__therapist-rating'>
                  {renderCategoryRatings()}
                </div>
              )}
              {/* Comment section */}
              <div className='ReviewItem__therapist-comment'>
                {_.get(review, 'comment.overall') || _.get(review, 'comment.booking') || _.get(review, 'comment.place')}
              </div>
            </div>
          </div>
        )}
      </div>
      <Confirm
        title={<FormattedMessage {...messages.confirmTitle} />}
        content={(
          <FormattedMessage
            {...messages.confirmContent}
          />
        )}
        show={confirm}
        handleHide={() => { setConfirm(false) }}
        handleAction={() => { removeReview() }}
        closeButton={<FormattedMessage {...messages.cancel} />}
        okButton={<FormattedMessage {...messages.ok} />}
        isShowIcon={false}
      />
      <ReviewForm
        key={review._id}
        onSubmit={values => { editReview(values) }}
        user={user}
        review={review}
        show={isShowEdit}
        onHide={() => { setIsShowEdit(false) }}
      />
    </React.Fragment>
  )
}

ReviewItem.propTypes = {
  intl: intlShape.isRequired,
  review: PropTypes.object,
  user: PropTypes.object,
  account: PropTypes.object,
  type: PropTypes.string,
  deleteReview: PropTypes.func,
  updateReview: PropTypes.func,
  getReviews: PropTypes.func
}
const mapStateToProps = createStructuredSelector({
  account: makeSelectCurrentUserData()
})

const mapDispatchToProps = dispatch => ({
  deleteReview: review => dispatch(deleteBookingReviews(review)),
  updateReview: review => dispatch(updateBookingReviews(review)),
  getReviews: bookingId => dispatch(getBookingReviews(bookingId))
})

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
)

export default compose(
  withConnect,
  injectIntl
)(ReviewItem)

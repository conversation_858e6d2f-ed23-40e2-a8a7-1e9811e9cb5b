.TherapistReviewItem {
  background-color: #EBEEF0;
  padding: 16px;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  width: 100%;

  // Avatar styling - 40x40px with proper shadow and border
  &__avatar {
    width: 40px;
    height: 40px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 50%;
    border: 1px solid #FFFFFF;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
    margin: 0;
    flex-shrink: 0;
  }

  // Main content section
  &__content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    min-width: 0;
  }

  // Top row: user info + date
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 100px;
    width: 100%;
  }

  // Left side: user info (name + tags)
  &__user-info {
    width: 258px;
    min-width: 0;
  }

  // Name and tags container
  &__name-tags {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  // User name styling
  .user-name {
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.448;
    color: #324452;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // Tags container
  &__tags {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
  }

  // Date styling - right aligned
  &__date {
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.448;
    color: #95A1AC;
    text-align: right;
    white-space: nowrap;
    flex-shrink: 0;
  }

  // Second row: star rating + subcategories
  &__rating-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 100px;
    width: 100%;
  }

  // Star rating section
  &__star-rating {
    width: 258px;
    display: flex;
    gap: 4px;
    font-size: 14px;

    i {
      &.fa-star {
        color: #FFC107;
      }

      &.fa-star-o {
        color: #FFC107;
        -webkit-text-stroke: 0.5px #FFC107;
        text-stroke: 0.5px #FFC107;
      }
    }
  }

  // Subcategories section - right aligned
  &__subcategories {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;

    .category-item {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 4px;

      .category-label {
        font-family: 'Noto Sans JP', sans-serif;
        font-weight: 400;
        font-size: 10px;
        line-height: 1.2;
        color: #5F6E7A;
        text-align: center;
        white-space: nowrap;
      }

      .category-icon {
        width: 12px;
        height: 12px;
        flex-shrink: 0;
      }
    }
  }

  // Comment section
  &__comment {
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.448;
    color: #324452;
    width: 100%;
    word-wrap: break-word;
    margin-top: 0;
  }

  // No review state
  &__no-review {
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #95A1AC;
    text-align: center;
    padding: 20px;
  }

  // Badge styling - blue theme per Figma
  .badge {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 1px 8px 2px;
    height: 20px;
    border: 1px solid #C1C9D1;
    border-radius: 4px;
    background-color: #FFFFFF;
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.448;
    color: #5F93FB;
    text-decoration: none;
    white-space: nowrap;

    &:hover {
      text-decoration: none;
      color: #5F93FB;
    }
  }

  .badge-role,
  .badge-booking {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 1px 8px 2px;
    height: 20px;
    border: 1px solid #C1C9D1;
    border-radius: 4px;
    background-color: #FFFFFF;
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 500;
    font-size: 12px;
    line-height: 1.448;
    color: #5F93FB;
    text-decoration: none;
    white-space: nowrap;

    &:hover {
      text-decoration: none;
      color: #5F93FB;
    }
  }

  // Action buttons
  &__actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    align-items: flex-end;
    position: absolute;
    top: 16px;
    right: 16px;

    .btn {
      width: 24px;
      height: 24px;
      background: #FFFFFF;
      border: 1px solid #DAE1E7;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 4px;
      cursor: pointer;

      &:hover {
        background: #F5F5F5;
      }

      i {
        font-size: 16px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.fa-pencil {
          color: #608099;
        }

        &.fa-trash {
          color: #888888;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    &__header {
      gap: 50px;
    }

    &__user-info {
      width: auto;
      flex: 1;
    }

    &__rating-section {
      gap: 50px;
      flex-direction: column;
      align-items: flex-start;
    }

    &__star-rating {
      width: auto;
    }

    &__subcategories {
      justify-content: flex-start;
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    padding: 12px;
    gap: 6px;

    &__header {
      flex-direction: column;
      gap: 8px;
    }

    &__date {
      text-align: left;
    }

    &__rating-section {
      gap: 8px;
    }

    &__actions {
      position: static;
      margin-top: 8px;
    }
  }
}

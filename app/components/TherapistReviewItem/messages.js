import { defineMessages } from 'react-intl'

export const scope = 'app.components.TherapistReviewItem'

export default defineMessages({
  booking: {
    id: `${scope}.booking`,
    defaultMessage: 'About this treatment'
  },
  place: {
    id: `${scope}.place`,
    defaultMessage: 'About visit place'
  },
  notReview: {
    id: `${scope}.notReview`,
    defaultMessage: 'Not review yet'
  },
  confirmTitle: {
    id: `${scope}.confirmTitle`,
    defaultMessage: 'Confirm'
  },
  confirmContent: {
    id: `${scope}.confirmContent`,
    defaultMessage: 'Are you sure delete review ?'
  },
  cancel: {
    id: `${scope}.cancel`,
    defaultMessage: 'Cancel'
  },
  ok: {
    id: `${scope}.ok`,
    defaultMessage: 'OK'
  }
})

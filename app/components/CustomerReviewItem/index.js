import React from 'react'
import PropTypes from 'prop-types'
import { injectIntl, intlShape } from 'react-intl'
import _ from 'lodash'
import moment from 'moment-timezone'
import defaultProfile from 'assets/img/default-profile.png'
import ratingHappyActive from 'assets/img/therapist-rating-happy-active.svg'
import ratingSadActive from 'assets/img/therapist-rating-sad-active.svg'
import './style.scss'

const CustomerReviewItem = ({ review, user = {}, intl }) => {
  const renderEmojiRating = () => {
    if (!review.rating) return null

    // Show happy emoji for rating >= 3, sad emoji for rating < 3
    const isHappy = review.rating >= 3
    const emojiIcon = isHappy ? ratingHappyActive : ratingSadActive

    return (
      <div className='customer-review-item__emoji'>
        <img src={emojiIcon} alt={isHappy ? 'Happy' : 'Sad'} />
      </div>
    )
  }

  const renderUserInfo = () => (
    <div className='customer-review-item__user-info'>
      <div className='customer-review-item__user-details'>
        <h4 className='customer-review-item__user-name'>
          {user.name || 'N/A'}
        </h4>
        <div className='customer-review-item__tags'>
          <span className='customer-review-item__tag customer-review-item__tag--role'>
            セラピスト
          </span>
          <span className='customer-review-item__tag customer-review-item__tag--id'>
            #
            {user._id || user.id || 'N/A'}
          </span>
        </div>
      </div>
    </div>
  )

  const renderDate = () => {
    if (!review.createdAt) return null

    return (
      <div className='customer-review-item__date'>
        {moment(review.createdAt).format('YYYY/MM/DD')}
      </div>
    )
  }

  const renderComment = () => {
    if (!review.comment) return null

    // Handle comment as object or string
    let commentText = ''

    if (typeof review.comment === 'object' && review.comment !== null) {
      // Comment is an object - only show overall property
      commentText = review.comment.overall || ''
    } else if (typeof review.comment === 'string') {
      // Comment is a string, use it directly
      commentText = review.comment
    }

    return (
      <div className='customer-review-item__comment'>
        {renderEmojiRating()}
        {commentText && (
          <div className='customer-review-item__comment-section'>
            <p>{commentText}</p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className='customer-review-item'>
      <div className='customer-review-item__avatar'>
        <img
          src={_.get(user, 'profilePicture.url') || defaultProfile}
          alt={user.name || 'User'}
        />
      </div>
      <div className='customer-review-item__content'>
        <div className='customer-review-item__header'>
          {renderUserInfo()}
          {renderDate()}
        </div>
        {renderComment()}
      </div>
    </div>
  )
}

CustomerReviewItem.propTypes = {
  intl: intlShape.isRequired,
  review: PropTypes.object.isRequired,
  user: PropTypes.object
}

export default injectIntl(CustomerReviewItem)

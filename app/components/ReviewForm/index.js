/* eslint-disable arrow-parens */
import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import happyEmoji from 'assets/img/happy-face.svg'
import sadEmoji from 'assets/img/sad-face.svg'
import { compose } from 'redux'
import _ from 'lodash'
import { createStructuredSelector } from 'reselect'
import { Modal } from 'react-bootstrap'
import {
  reduxForm,
  Field,
  reset,
  formValueSelector
} from 'redux-form/immutable'
import { intlShape, injectIntl, FormattedMessage } from 'react-intl'
import ReactStars from 'react-rating-stars-component'
import globalMessages from 'utils/globalMessages'
import defaultProfile from 'assets/img/default-profile.png'
import messages from './messages'
import './style.scss'
import validate from './validate'

export const REVIEW_FORM_ID = 'reviewForm'

const renderCommentTextarea = ({ input, isCustomer }) => (
  <div className='textarea-wrapper'>
    <textarea
      {...input}
      className='comment-textarea'
      placeholder={isCustomer ? '初めてでしたが頼んで良かったです。 またお願いします！' : ''}
      maxLength={500}
      rows={4}
    />
  </div>
)
renderCommentTextarea.propTypes = {
  input: PropTypes.object,
  isCustomer: PropTypes.bool
}

// Category Rating Component for Customer Reviews
const CategoryRating = ({
  category,
  value,
  onChange,
  intl,
  hideLabel
}) => {
  const isGood = value === 'GOOD'
  const isBad = value === 'BAD'

  return (
    <div className='category-rating'>
      {!hideLabel && (
        <div className='category-label'>
          <FormattedMessage {...messages.categories[category]} />
        </div>
      )}
      <div className='rating-options'>
        <div
          className={`rating-option ${isGood ? 'active happy' : ''}`}
          onClick={() => onChange('GOOD')}
        >
          <div className='emoji-container'>
            <span className='emoji happy-emoji' role='img' aria-label='Happy face'><img src={happyEmoji} alt='Happy face' style={{ width: '16px', height: '16px' }} /></span>
          </div>
          <span className='option-text'>良かった</span>
        </div>
        <div
          className={`rating-option ${isBad ? 'active sad' : ''}`}
          onClick={() => onChange('BAD')}
        >
          <div className='emoji-container'>
            <span className='emoji sad-emoji' role='img' aria-label='Sad face'><img src={sadEmoji} alt='Sad face' style={{ width: '16px', height: '16px' }} /></span>
          </div>
          <span className='option-text'>残念だった</span>
        </div>
      </div>
    </div>
  )
}

CategoryRating.propTypes = {
  category: PropTypes.string.isRequired,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  intl: intlShape.isRequired,
  hideLabel: PropTypes.bool
}

const ReviewForm = ({
  review,
  user = {},
  intl,
  show,
  onHide,
  submitting,
  valid,
  handleSubmit,
  change,
  resetForm,
  overallComment
}) => {
  const [categoryRatings, setCategoryRatings] = useState({
    technique: null,
    service: null,
    cost: null,
    therapistImpression: null
  })
  const [overallRating, setOverallRating] = useState(0)

  useEffect(() => {
    resetForm()
    if (!show) {
      return
    }

    // Initialize form data from existing review
    if (review.comment) {
      Object.keys(review.comment).forEach(key => {
        change(key, review.comment[key])
      })
    }

    // Initialize ratings
    if (review.rating) {
      setOverallRating(review.rating)
      change('rating', review.rating)
    }

    // Initialize category ratings if they exist
    if (review.categories) {
      setCategoryRatings({
        technique: review.categories.technique || null,
        service: review.categories.service || null,
        cost: review.categories.cost || null,
        therapistImpression: review.categories.therapistImpression || null
      })
    }
  }, [show, review])

  const handleCategoryRatingChange = (category, value) => {
    const newRatings = { ...categoryRatings, [category]: value }
    setCategoryRatings(newRatings)
    change(`categories.${category}`, value)
  }

  const handleStarRatingChange = newRating => {
    setOverallRating(newRating)
    change('rating', newRating)
  }

  const isCustomer = user.role === 'customer'

  return (
    <Modal show={show} backdrop='static' onHide={() => onHide(false)} className='updateReview'>
      <Modal.Header closeButton className='review-modal-header'>
        <Modal.Title className='review-modal-title'>
          <FormattedMessage {...messages.header} />
        </Modal.Title>
      </Modal.Header>

      <form onSubmit={handleSubmit}>
        <Modal.Body className='review-modal-body'>
          {/* Booking ID Section */}
          <div className='booking-id-section'>
            <FormattedMessage {...messages.bookingId} />
            {`: ${review.bookingId}`}
          </div>

          {/* User Info Section */}
          <div className='user-info-section'>
            <div className='user-details'>
              <div className='user-avatar'>
                <img
                  src={_.get(user, 'avatar') ||
                  _.get(user, 'profilePicture.url') ||
                  defaultProfile}
                  className='avatar-image'
                  alt='User avatar'
                />
              </div>
              <div className='user-name-role'>
                <div className='user-name'>
                  {_.truncate((user.name || user.fullName || intl.formatMessage(globalMessages.anonymous)), { length: 50 })}
                </div>
                <div className='user-role'>
                  {intl.formatMessage(globalMessages[`role.${user.role}`])}
                </div>
              </div>
            </div>
          </div>

          {/* Customer Category Ratings */}
          {isCustomer && (
            <div className='category-ratings-section'>
              <CategoryRating
                category='technique'
                value={categoryRatings.technique}
                onChange={(value) => handleCategoryRatingChange('technique', value)}
                intl={intl}
              />
              <CategoryRating
                category='service'
                value={categoryRatings.service}
                onChange={(value) => handleCategoryRatingChange('service', value)}
                intl={intl}
              />
              <CategoryRating
                category='cost'
                value={categoryRatings.cost}
                onChange={(value) => handleCategoryRatingChange('cost', value)}
                intl={intl}
              />

              {/* Overall Star Rating */}
              <div className='overall-rating-section'>
                <div className='overall-label'>
                  <FormattedMessage {...messages.overallRating} />
                </div>
                <div className='star-rating'>
                  <ReactStars
                    count={5}
                    name='rating'
                    value={overallRating}
                    onChange={handleStarRatingChange}
                    size={32}
                    activeColor='#FFB300'
                    color='#C1C9D1'
                  />
                </div>
              </div>
            </div>
          )}

          {/* Therapist Review Fields */}
          {!isCustomer && (
            <div className='therapist-review-section'>
              <CategoryRating
                category='therapistImpression'
                value={categoryRatings.therapistImpression}
                onChange={(value) => handleCategoryRatingChange('therapistImpression', value)}
                intl={intl}
                hideLabel
              />
            </div>
          )}

          {/* Comment Section */}
          <div className='comment-section'>
            <div className='comment-textarea-container'>
              <Field
                name='overall'
                component={renderCommentTextarea}
                isCustomer={isCustomer}
              />
            </div>
            <div className='character-counter'>
              {`${overallComment ? overallComment.length : 0}/500`}
            </div>
          </div>

        </Modal.Body>

        <Modal.Footer className='review-modal-footer'>
          <div className='footer-actions'>
            <button
              type='button'
              className='btn-cancel'
              onClick={() => onHide(true)}
            >
              <FormattedMessage {...messages.cancel} />
            </button>
            <button
              type='submit'
              className='btn-save'
              disabled={!valid || submitting}
            >
              <FormattedMessage {...messages.save} />
            </button>
          </div>
        </Modal.Footer>
      </form>
    </Modal>
  )
}

ReviewForm.propTypes = {
  review: PropTypes.object,
  user: PropTypes.object,
  show: PropTypes.bool,
  onHide: PropTypes.any,
  submitting: PropTypes.any,
  valid: PropTypes.any,
  handleSubmit: PropTypes.func,
  change: PropTypes.any,
  resetForm: PropTypes.any,
  intl: intlShape.isRequired,
  overallComment: PropTypes.string
}

ReviewForm.defaultProps = {
  show: false,
  onHide: () => { }
}

const mapDispatchToProps = dispatch => ({
  resetForm: () => dispatch(reset(REVIEW_FORM_ID))
})

const selector = formValueSelector(REVIEW_FORM_ID)

const mapStateToProps = createStructuredSelector({
  overallComment: state => selector(state, 'overall')
})

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
)

export default compose(
  withConnect,
  reduxForm({
    form: REVIEW_FORM_ID,
    validate,
    enableReinitialize: true
  }), injectIntl
)(ReviewForm)

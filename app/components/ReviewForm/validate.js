import messages from './messages'

const validate = values => {
  const errors = {}

  // Existing therapist validation
  const commentBooking = values.has('booking') && values.get('booking')
  if (!commentBooking || commentBooking.length < 3) {
    errors.booking = messages.validate.minimum
  }

  const commentPlace = values.has('place') && values.get('place')
  if (!commentPlace || commentPlace.length < 3) {
    errors.place = messages.validate.minimum
  }

  const therapist = values.has('therapist') && values.get('therapist')
  if (therapist && therapist.length < 3) {
    errors.therapist = messages.validate.minimum
  }

  // New customer category validation
  const rating = values.get('rating')
  const categories = values.get('categories')
  
  // For customer reviews, star rating is required
  if (rating !== undefined && rating !== null) {
    if (!rating || rating < 1) {
      errors.rating = messages.validate.ratingRequired
    }
  }

  // Category ratings validation for customers
  if (categories) {
    const categoryErrors = {}
    
    // Check if all required categories are selected
    const requiredCategories = ['technique', 'service', 'cost']
    requiredCategories.forEach(category => {
      const categoryValue = categories.get ? categories.get(category) : categories[category]
      if (!categoryValue || (categoryValue !== 'GOOD' && categoryValue !== 'BAD')) {
        categoryErrors[category] = messages.validate.categoryRequired
      }
    })
    
    if (Object.keys(categoryErrors).length > 0) {
      errors.categories = categoryErrors
    }
  }

  // Overall comment validation (500 character limit)
  const overall = values.get('overall')
  if (overall && overall.length > 500) {
    errors.overall = messages.validate.maxLength
  }

  return errors
}

export default validate

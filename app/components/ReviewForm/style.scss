// Update Review Modal
.updateReview {
  .modal-dialog {
    max-width: 560px;
    margin: 2rem auto;
  }

  .modal-content {
    border-radius: 4px;
    border: none;
    box-shadow: 0px 8px 32px rgba(0, 0, 0, 0.1);
  }

  // Header with gradient background
  .review-modal-header {
    background: linear-gradient(135deg, rgba(255, 210, 128, 0.2) 0%, rgba(255, 240, 212, 0.2) 27.79%, rgba(255, 246, 229, 0.2) 49.67%, rgba(204, 233, 255, 0.2) 75.38%, rgba(107, 170, 220, 0.2) 100%);
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    padding: 25px 16px 25px 24px;
    min-height: auto;

    .review-modal-title {
      font-family: 'Noto Sans JP', sans-serif;
      font-size: 20px;
      font-weight: 500;
      line-height: 1.448;
      color: #324452;
      margin: 0;
    }

    .close {
      font-size: 24px;
      color: #608099;
      opacity: 1;
      text-shadow: none;
      outline: none;
      
      &:hover {
        color: #324452;
      }
    }
  }

  // Modal Body
  .review-modal-body {
    padding: 16px 24px 24px;
    background: #FFFFFF;
  }

  // Booking ID Section
  .booking-id-section {
    font-family: 'Noto Sans JP', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.448;
    color: #A7B5C0;
    margin-bottom: 16px;
  }

  // User Info Section
  .user-info-section {
    margin-bottom: 24px;

    .user-details {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .user-avatar {
      .avatar-image {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        border: 1px solid #FFFFFF;
        object-fit: cover;
      }
    }

    .user-name-role {
      display: flex;
      flex-direction: column;
      justify-content: center;
      .user-name {
        font-size: 14px;
        font-weight: 500;
        color: #324452;
        margin-bottom: 4px;
      }

      .user-role {
        font-size: 12px;
        font-weight: 400;
        color:#A7B5C0;
      }
    }
  }

  .category-rating {
    height: 72px;
    position: relative;
    margin-bottom: 16px;

    .category-label {
      position: absolute;
      top: 24px;
      left: 0;
      width: 56px;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.429;
      text-align: center;
      color: #3C3C3C;
    }

    .rating-options {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 32px;
    }

    .rating-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 16px 8px 0;
      cursor: pointer;
      border-radius: 32px;
      transition: all 0.2s ease;

      .emoji-container {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .rating-emoji {
        width: 24px;
        height: 24px;
      }

      .option-text {
        font-family: 'Noto Sans JP', sans-serif;
        font-size: 12px;
        font-weight: 500;
        line-height: 1.333;
        text-align: center;
        color: #5F6E7A;
        width: 60px;
        white-space: nowrap;
      }

      &.active {
        .option-text {
          color: #324452;
        }
      }
    }
  }

  // Category Ratings Section
  .category-ratings-section {
    padding: 0 8px;
    margin-bottom: 24px;

    // Overall Rating Section
    .overall-rating-section {
      height: 48px;
      position: relative;
      margin-top: 16px;

      .overall-label {
        position: absolute;
        top: 14px;
        left: 0;
        width: 56px;
        font-family: 'Noto Sans JP', sans-serif;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.429;
        text-align: center;
        color: #3C3C3C;
      }

      .star-rating {
        position: absolute;
        top: 8px;
        left: 152px;
        display: flex;
        gap: 8px;
      }
    }
  }

  // Comment Section
  .comment-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-bottom: 24px;

    .comment-textarea-container {
      width: 100%;
      margin-bottom: 8px;

      .textarea-wrapper {
        position: relative;
        width: 512px;
        height: 80px;

        .comment-textarea {
          width: 100%;
          height: 100%;
          padding: 6px 8px;
          border: 1px solid #A7B5C0;
          border-radius: 4px;
          font-family: 'Noto Sans JP', sans-serif;
          font-size: 14px;
          font-weight: 400;
          line-height: 1.448;
          color: #324452;
          background: #FFFFFF;
          resize: none;
          outline: none;
          
          &:focus {
            border-color: #608099;
            box-shadow: 0 0 0 1px rgba(96, 128, 153, 0.2);
          }

          &::placeholder {
            color: #A7B5C0;
          }
        }

        .expand-icon {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 16px;
          height: 16px;
          padding: 2px;
          cursor: pointer;
          
          svg {
            width: 12px;
            height: 12px;
          }
        }
      }
    }

    .character-counter {
      font-family: 'Noto Sans JP', sans-serif;
      font-size: 12px;
      font-weight: 400;
      line-height: 1.448;
      text-align: right;
      color: #A7B5C0;
    }
  }

  // Therapist Review Section (Legacy)
  .therapist-review-section {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    .category-rating {
      height: auto;
      margin-bottom: 0;
    }
  }

  // Modal Footer
  .review-modal-footer {
    background: #FFFFFF;
    border-top: 2px solid #FFE8BF;
    border-radius: 0 0 4px 4px;
    padding: 24px;

    .footer-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      justify-content: flex-end;
    }

    .btn-cancel {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
      padding: 6px 16px;
      background: #FFFFFF;
      border: 1px solid #608099;
      border-radius: 4px;
      font-family: 'Noto Sans JP', sans-serif;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.429;
      text-align: center;
      color: #608099;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0px 4px 8px rgba(96, 128, 153, 0.2);

      &:hover {
        background: #f8f9fa;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .btn-save {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 4px;
      padding: 6px 16px;
      background: linear-gradient(135deg, #608099 0%, #5894C7 100%);
      border: none;
      border-radius: 4px;
      font-family: 'Noto Sans JP', sans-serif;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.429;
      text-align: center;
      color: #FFFFFF;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0px 4px 8px rgba(96, 128, 153, 0.4);

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0px 6px 12px rgba(96, 128, 153, 0.5);
      }

      &:active:not(:disabled) {
        transform: translateY(0);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }

  
}
import { defineMessages } from 'react-intl'

const scope = 'app.components.ReviewForm'

export default defineMessages({
  header: {
    id: `${scope}.header`,
    defaultMessage: 'Update Review'
  },
  bookingId: {
    id: `${scope}.bookingId`,
    defaultMessage: 'Booking ID'
  },
  reviewBooking: {
    id: `${scope}.reviewBooking`,
    defaultMessage: 'About this treatment'
  },
  reviewPlace: {
    id: `${scope}.reviewPlace`,
    defaultMessage: 'About visit place'
  },
  overallRating: {
    id: `${scope}.overallRating`,
    defaultMessage: '総合評価'
  },
  categories: {
    technique: {
      id: `${scope}.categories.technique`,
      defaultMessage: '技術'
    },
    service: {
      id: `${scope}.categories.service`,
      defaultMessage: '接客'
    },
    cost: {
      id: `${scope}.categories.cost`,
      defaultMessage: 'コスパ'
    }
  },
  cancel: {
    id: `${scope}.cancel`,
    defaultMessage: 'Cancel'
  },
  save: {
    id: `${scope}.save`,
    defaultMessage: 'Save'
  },
  validate: {
    booking: {
      id: `${scope}.validate.booking`,
      defaultMessage: 'Comment review treatment is required'
    },
    place: {
      id: `${scope}.validate.place`,
      defaultMessage: 'Comment place is required'
    },
    minimum: {
      id: `${scope}.validate.minimum`,
      defaultMessage: 'Minimum 3 character'
    },
    ratingRequired: {
      id: `${scope}.validate.ratingRequired`,
      defaultMessage: 'Star rating is required'
    },
    categoryRequired: {
      id: `${scope}.validate.categoryRequired`,
      defaultMessage: 'Please select a rating for this category'
    },
    maxLength: {
      id: `${scope}.validate.maxLength`,
      defaultMessage: 'Comment cannot exceed 500 characters'
    }
  }
})

import { defineMessages } from 'react-intl'

export const scope = 'app.components.ReviewTab'

export default defineMessages({
  reviews: {
    id: `${scope}.reviews`,
    defaultMessage: 'Reviews'
  },
  reviewRating: {
    id: `${scope}.reviewRating`,
    defaultMessage: 'Review rating'
  },
  averageRating: {
    id: `${scope}.averageRating`,
    defaultMessage: 'Average rating'
  },
  reviewList: {
    id: `${scope}.reviewList`,
    defaultMessage: 'Review list'
  },
  stars: {
    id: `${scope}.stars`,
    defaultMessage: 'stars'
  },
  descriptionAverageRating: {
    id: `${scope}.descriptionAverageRating`,
    defaultMessage: 'Based on {sumReviewer} reviews'
  },
  noReview: {
    id: `${scope}.noReview`,
    defaultMessage: 'No reviews'
  },
  loadMore: {
    id: `${scope}.loadMore`,
    defaultMessage: 'Load more'
  }
})

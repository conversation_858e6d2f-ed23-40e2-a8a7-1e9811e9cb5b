.ReviewTab {
  background: #f2f2f7;
  padding: 15px 144px;
  min-height: calc(100vh - 150px);
  .card {
    border-width: 0;
    border-radius: 2px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05),
      1px 0px 2px 0 rgba(63, 63, 68, 0.1);
    &-header {
      background: none;
      font-weight: bold;
      font-size: 16px;
    }
  }
  &__averageRating {
    padding: 16px 0;
    text-align: center;
    
    strong {
      font-size: 36px;
      line-height: 1.2;
      
      .fa {
        color: #f4bd1a;
        margin-left: 8px;
      }
    }
    
    p {
      margin: 8px 0 0;
      color: #758ea3;
    }
  }
  
  &__ratings {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      span {
        &:first-child {
          flex: 0 0 80px;
          font-size: 14px;
          color: #758ea3;
        }
        
        &:last-child {
          flex: 0 0 60px;
          text-align: right;
          font-size: 14px;
          color: #324452;
          font-weight: 500;
        }
      }
    }
    
    &__process {
      flex: 1;
      margin: 0 12px;
      
      .progress {
        height: 8px;
        background-color: #e4ebef;
        border-radius: 4px;
        
        .progress-bar {
          background-color: #f4bd1a;
          border-radius: 4px;
        }
      }
    }
  }

  &__subcategory-statistics {
    margin-top: 24px !important;
    padding: 0;
    
    // Reset list item flex layout for the subcategory component
    display: block !important;
  }
  
  &__reviews {
    .card-body {
      padding: 24px;
    }
    
    .ReviewItem + .ReviewItem,
    .TherapistReviewItem + .TherapistReviewItem,
    .ReviewItem + .TherapistReviewItem,
    .TherapistReviewItem + .ReviewItem {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #e4ebef;
    }
    
    // Ensure TherapistReviewItem has consistent spacing
    .TherapistReviewItem {
      margin-bottom: 0;
      
      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }
  }
  
  &__action {
    text-align: center;
    margin-top: 24px;
    
    .btn {
      min-width: 120px;
    }
    
    .fa-spin {
      font-size: 24px;
      color: #758ea3;
    }
  }
}

.ReviewTab__customer-summary {
  text-align: center;
  padding: 16px 0;
}

.ReviewTab__total-reviews {
  text-align: center;
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: #324452;
  margin-bottom: 16px;
}

.ReviewTab__good-bad-summary {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
  margin: 8px 0 0 0;
}

.ReviewTab__good, .ReviewTab__bad {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.ReviewTab__good img, .ReviewTab__bad img {
  width: 32px;
  height: 32px;
  margin-bottom: 4px;
}

.ReviewTab__good span, .ReviewTab__bad span {
  font-family: 'Noto Sans JP', sans-serif;
  font-size: 20px;
  font-weight: 700;
  color: #324452;
}

@media (max-width: 600px) {
  .ReviewTab__good-bad-summary {
    gap: 16px;
  }
  .ReviewTab__good img, .ReviewTab__bad img {
    width: 24px;
    height: 24px;
  }
  .ReviewTab__good span, .ReviewTab__bad span {
    font-size: 16px;
  }
}
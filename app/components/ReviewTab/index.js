import React, { useState, useEffect } from 'react'
import { ProgressBar } from 'react-bootstrap'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { compose } from 'redux'
import { createStructuredSelector } from 'reselect'
import {
  injectIntl, intlShape, FormattedMessage
} from 'react-intl'
import _ from 'lodash'
import { PAGINATION } from 'utils/constants'
import ReviewItem from 'components/ReviewItem'
import SubcategoryReviewSummary from 'components/SubcategoryReviewSummary'
import { getTherapistReviews, getCustomerReviews, resetReviews } from 'containers/Review/actions'
import { getCustomerReviews as getCustomerReviewsService } from 'Services/ReviewService'
import {
  makeSelectReviewList,
  makeSelectIsLoading,
  makeSelectSummaryReview,
  makeSelectReviewPagination,
  makeSelectReviewGroupByRating
} from 'containers/Review/selectors'
import messages from './messages'
import './style.scss'

const ReviewTab = ({
  intl,
  user,
  reviews,
  summaryReview,
  reviewGroupByRating,
  isLoading,
  pagination,
  getReviews,
  unsetReviews
}) => {
  const [allReview, setAllReview] = useState([])
  const [customerReviews, setCustomerReviews] = useState([])
  const [query, setQuery] = useState({
    page: PAGINATION.PAGE,
    limit: PAGINATION.LIMIT
  })
  const [groupStars, setGroupStars] = useState({
    1: 0,
    2: 0,
    3: 0,
    4: 0,
    5: 0
  })

  useEffect(() => {
    user._id && getReviews(user.role, { userId: user._id, ...query })
    return unsetReviews
  }, [])

  // Fetch customer reviews about this therapist for statistics
  useEffect(() => {
    if (user.role === 'therapist' && user._id) {
      const fetchCustomerReviews = async () => {
        try {
          const response = await getCustomerReviewsService({
            userId: user._id,
            page: 1,
            limit: 1000 // Get all customer reviews for statistics
          })
          if (response && response.reviews && response.reviews.data) {
            setCustomerReviews(response.reviews.data)
          }
        } catch (error) {
          console.warn('Failed to fetch customer reviews for statistics:', error)
        }
      }
      fetchCustomerReviews()
    }
  }, [user._id, user.role])

  useEffect(
    () => {
      setAllReview([...allReview, ...reviews])
    },
    [reviews],
  )

  useEffect(
    () => {
      const data = {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0
      }
      reviewGroupByRating.map(i => (data[i.rating] += i.count))
      return setGroupStars(data)
    },
    [reviewGroupByRating],
  )

  const loadMore = () => {
    const params = {
      page: pagination.page + 1,
      limit: query.limit
    }
    getReviews(user.role, { userId: user._id, ...params })
    setQuery(params)
  }

  return (
    <section className='ReviewTab row'>
      <article className='col-sm-4'>
        <div className='card'>
          <div className='card-header'>
            {intl.formatMessage(messages.averageRating)}
          </div>
          <div className='card-body'>
            <div className='ReviewTab__averageRating'>
              <strong>
                {summaryReview.sumRating && summaryReview.sumReviewer ? _.round(summaryReview.sumRating / summaryReview.sumReviewer, 1) : 0}
                <i className='fa fa-star' />
              </strong>
              <p>
                {intl.formatMessage(messages.descriptionAverageRating, {
                  sumReviewer: summaryReview.sumReviewer || 0
                })}
              </p>
            </div>
            <ul className='ReviewTab__ratings'>
              {Object.keys(groupStars)
                .sort()
                .reverse()
                .map(i => {
                  const percent = groupStars[i] && summaryReview.sumReviewer ? groupStars[i] / summaryReview.sumReviewer * 100 : 0
                  return (
                    <li key={`li_${i}_${reviews._id}_${new Date().getTime()}`}>
                      <span>
                        {intl.formatMessage(messages.stars)}
                        {' '}
                        {i}
                      </span>
                      <span className='ReviewTab__ratings__process'>
                        <ProgressBar now={percent} />
                      </span>
                      <span>
                        {percent > 0 && '~'}
                        {Math.round(percent)}
                        %
                      </span>
                    </li>
                  )
                })}
              {/* Add customer review statistics for therapists inside ratings */}
              {user.role === 'therapist' && (
                <li className='ReviewTab__subcategory-statistics'>
                  <SubcategoryReviewSummary customerReviews={customerReviews} />
                </li>
              )}
            </ul>
          </div>
        </div>
      </article>
      <article className='col-sm-8'>
        <div className='card ReviewTab__reviews'>
          <div className='card-header'>
            <FormattedMessage {...messages.reviewList} />
          </div>
          <div className='card-body'>
            {allReview.length
              ? allReview.map(review => (
                <ReviewItem
                  key={`ReviewItem_${review._id}_${new Date().getTime()}`}
                  review={review}
                  lastPercent
                  user={{ ...review.reviewer, role: user.role === 'therapist' ? 'customer' : 'therapist' }}
                  type='ReviewTab'
                />
              ))
              : !isLoading && <i>{intl.formatMessage(messages.noReview)}</i>}
            <div className='ReviewTab__action'>
              {!isLoading && pagination.page < pagination.lastPage && (
                <button
                  type='button'
                  className='btn btn-light'
                  onClick={loadMore}
                >
                  {intl.formatMessage(messages.loadMore)}
                </button>
              )}
              {isLoading && <i className='fa fa-circle-o-notch fa-spin' />}
            </div>
          </div>
        </div>
      </article>
    </section>
  )
}

ReviewTab.propTypes = {
  intl: intlShape.isRequired,
  isLoading: PropTypes.bool,
  user: PropTypes.object,
  pagination: PropTypes.object,
  reviews: PropTypes.array,
  summaryReview: PropTypes.object,
  reviewGroupByRating: PropTypes.array,
  getReviews: PropTypes.func,
  unsetReviews: PropTypes.func
}

const mapDispatchToProps = dispatch => ({
  getReviews: (role, query) => dispatch(role === 'therapist' ? getTherapistReviews(query) : getCustomerReviews(query)),
  unsetReviews: () => dispatch(resetReviews())
})
const mapStateToProps = createStructuredSelector({
  reviews: makeSelectReviewList(),
  summaryReview: makeSelectSummaryReview(),
  pagination: makeSelectReviewPagination(),
  isLoading: makeSelectIsLoading(),
  reviewGroupByRating: makeSelectReviewGroupByRating()
})

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
)

export default compose(withConnect, injectIntl)(ReviewTab)

import React, { useState, useEffect } from 'react'
import { ProgressBar } from 'react-bootstrap'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { compose } from 'redux'
import { createStructuredSelector } from 'reselect'
import {
  injectIntl, intlShape, FormattedMessage
} from 'react-intl'
import _ from 'lodash'
import { PAGINATION } from 'utils/constants'
import ReviewItem from 'components/ReviewItem'
import TherapistReviewItem from 'components/TherapistReviewItem'
import CustomerReviewItem from 'components/CustomerReviewItem'
import ratingHappyActive from 'assets/img/therapist-rating-happy-active.svg'
import ratingSadActive from 'assets/img/therapist-rating-sad-active.svg'
import SubcategoryReviewSummary from 'components/SubcategoryReviewSummary'
import { getTherapistReviews, getCustomerReviews, resetReviews } from 'containers/Review/actions'
import { getCustomerReviews as getCustomerReviewsService } from 'Services/ReviewService'
import {
  makeSelectReviewList,
  makeSelectIsLoading,
  makeSelectSummaryReview,
  makeSelectReviewPagination,
  makeSelectReviewGroupByRating,
  makeSelectCategorySummary
} from 'containers/Review/selectors'
import messages from './messages'
import './style.scss'

const ReviewTab = ({
  intl,
  user = {},
  reviews = [],
  summaryReview = {},
  reviewGroupByRating = [],
  categorySummary = {},
  isLoading = false,
  pagination = {},
  getReviews = () => {},
  unsetReviews = () => {}
}) => {
  if (!intl || !user._id) {
    return <div>Loading...</div>
  }
  const [allReview, setAllReview] = useState([])
  const [customerReviews, setCustomerReviews] = useState([])
  const [query, setQuery] = useState({
    page: PAGINATION.PAGE,
    limit: PAGINATION.LIMIT
  })
  const [groupStars, setGroupStars] = useState({
    1: 0,
    2: 0,
    3: 0,
    4: 0,
    5: 0
  })

  useEffect(() => {
    if (user._id && user.role && getReviews) {
      getReviews(user.role, { userId: user._id, ...query })
    }
    return () => {
      if (unsetReviews) {
        unsetReviews()
      }
    }
  }, [user._id, user.role])

  // Fetch customer reviews about this therapist for statistics
  useEffect(() => {
    if (user.role === 'therapist' && user._id) {
      const fetchCustomerReviews = async () => {
        try {
          const response = await getCustomerReviewsService({
            userId: user._id,
            page: 1,
            limit: 1000 // Get all customer reviews for statistics
          })
          if (response && response.reviews && response.reviews.data) {
            setCustomerReviews(response.reviews.data)
          }
        } catch (error) {
          console.warn('Failed to fetch customer reviews for statistics:', error)
        }
      }
      fetchCustomerReviews()
    }
  }, [user._id, user.role])

  useEffect(
    () => {
      if (reviews && Array.isArray(reviews)) {
        setAllReview(prevReviews => [...prevReviews, ...reviews])
      }
    },
    [reviews],
  )

  useEffect(
    () => {
      const data = {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0
      }
      if (reviewGroupByRating && Array.isArray(reviewGroupByRating)) {
        reviewGroupByRating.forEach(i => {
          if (i && i.rating && i.count) {
            data[i.rating] += i.count
          }
        })
      }
      setGroupStars(data)
    },
    [reviewGroupByRating],
  )

  // Get good/bad counts from categorySummary for customer role
  const goodCount = (categorySummary && categorySummary.overall && categorySummary.overall.GOOD) || 0
  const badCount = (categorySummary && categorySummary.overall && categorySummary.overall.BAD) || 0

  const loadMore = () => {
    if (!pagination || !pagination.page || !user._id || !user.role || !getReviews) {
      return
    }
    const params = {
      page: pagination.page + 1,
      limit: query.limit
    }
    getReviews(user.role, { userId: user._id, ...params })
    setQuery(params)
  }

  return (
    <section className='ReviewTab row'>
      <article className='col-sm-4'>
        <div className='card'>
          <div className='card-header'>
            {user.role === 'customer'
              ? intl.formatMessage(messages.reviewRating)
              : intl.formatMessage(messages.averageRating)
            }
          </div>
          <div className='card-body'>
            {user.role === 'customer' ? (

              <div className='ReviewTab__customer-summary'>
                <div className='ReviewTab__total-reviews'>
                  {`${goodCount + badCount}件のレビュー`}
                </div>
                <div className='ReviewTab__good-bad-summary'>
                  <div className='ReviewTab__good'>
                    <img src={ratingHappyActive} alt='Good' />
                    <span>
                      {goodCount}
                      件
                    </span>
                  </div>
                  <div className='ReviewTab__bad'>
                    <img src={ratingSadActive} alt='Bad' />
                    <span>
                      {badCount}
                      件
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              // Therapist role: Show traditional average rating with stars
              <>
                <div className='ReviewTab__averageRating'>
                  <strong>
                    {summaryReview.sumRating && summaryReview.sumReviewer ? _.round(summaryReview.sumRating / summaryReview.sumReviewer, 1) : 0}
                    <i className='fa fa-star' />
                  </strong>
                  <p>
                    {intl.formatMessage(messages.descriptionAverageRating, {
                      sumReviewer: summaryReview.sumReviewer || 0
                    })}
                  </p>
                </div>
                <ul className='ReviewTab__ratings'>
                  {Object.keys(groupStars)
                    .sort()
                    .reverse()
                    .map(i => {
                      const percent = groupStars[i] && summaryReview.sumReviewer ? groupStars[i] / summaryReview.sumReviewer * 100 : 0
                      return (
                        <li key={`li_${i}_${new Date().getTime()}`}>
                          <span>
                            {intl.formatMessage(messages.stars)}
                            {' '}
                            {i}
                          </span>
                          <span className='ReviewTab__ratings__process'>
                            <ProgressBar now={percent} />
                          </span>
                          <span>
                            {percent > 0 && '~'}
                            {Math.round(percent)}
                            %
                          </span>
                        </li>
                      )
                    })}
                  {/* Add customer review statistics for therapists inside ratings */}
                  {user.role === 'therapist' && (
                    <li className='ReviewTab__subcategory-statistics'>
                      <SubcategoryReviewSummary customerReviews={customerReviews} />
                    </li>
                  )}
                </ul>
              </>
            )}
          </div>
        </div>
      </article>
      <article className='col-sm-8'>
        <div className='card ReviewTab__reviews'>
          <div className='card-header'>
            <FormattedMessage {...messages.reviewList} />
          </div>
          <div className='card-body'>
            {allReview && allReview.length
              ? allReview.map((review, index) => {
                if (!review || !review._id) {
                  return null
                }
                // Choose component based on user role
                let ReviewComponent = ReviewItem
                if (user.role === 'customer') {
                  ReviewComponent = CustomerReviewItem
                } else if (user.role === 'therapist') {
                  ReviewComponent = TherapistReviewItem
                }

                return (
                  <ReviewComponent
                    key={`ReviewItem_${review._id}_${index}`}
                    review={review}
                    user={{
                      ...(review.reviewer || {}),
                      role: user.role === 'therapist' ? 'customer' : 'therapist'
                    }}
                    type='ReviewTab'
                  />
                )
              })
              : !isLoading && <i>{intl.formatMessage(messages.noReview)}</i>}
            <div className='ReviewTab__action'>
              {!isLoading && pagination.page < pagination.lastPage && (
                <button
                  type='button'
                  className='btn btn-light'
                  onClick={loadMore}
                >
                  {intl.formatMessage(messages.loadMore)}
                </button>
              )}
              {isLoading && <i className='fa fa-circle-o-notch fa-spin' />}
            </div>
          </div>
        </div>
      </article>
    </section>
  )
}

ReviewTab.propTypes = {
  intl: intlShape.isRequired,
  isLoading: PropTypes.bool,
  user: PropTypes.object,
  pagination: PropTypes.object,
  reviews: PropTypes.array,
  summaryReview: PropTypes.object,
  reviewGroupByRating: PropTypes.array,
  categorySummary: PropTypes.object,
  getReviews: PropTypes.func,
  unsetReviews: PropTypes.func
}

ReviewTab.defaultProps = {
  isLoading: false,
  user: {},
  pagination: {},
  reviews: [],
  summaryReview: {},
  reviewGroupByRating: [],
  categorySummary: {},
  getReviews: () => {},
  unsetReviews: () => {}
}

const mapDispatchToProps = dispatch => ({
  getReviews: (role, query) => dispatch(role === 'therapist' ? getTherapistReviews(query) : getCustomerReviews(query)),
  unsetReviews: () => dispatch(resetReviews())
})
const mapStateToProps = createStructuredSelector({
  reviews: makeSelectReviewList(),
  summaryReview: makeSelectSummaryReview(),
  pagination: makeSelectReviewPagination(),
  isLoading: makeSelectIsLoading(),
  reviewGroupByRating: makeSelectReviewGroupByRating(),
  categorySummary: makeSelectCategorySummary()
})

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
)

export default compose(withConnect, injectIntl)(ReviewTab)

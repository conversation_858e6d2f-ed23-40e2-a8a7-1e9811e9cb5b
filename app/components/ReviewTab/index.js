import CustomerReviewItem from 'components/CustomerReviewItem'
import ratingHappyActive from 'assets/img/therapist-rating-happy-active.svg'
import ratingSadActive from 'assets/img/therapist-rating-sad-active.svg'
import SubcategoryReviewSummary from 'components/SubcategoryReviewSummary'
import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { compose } from 'redux'
import { createStructuredSelector } from 'reselect'
import _ from 'lodash'
import {
  injectIntl, intlShape, FormattedMessage
} from 'react-intl'
import ReviewItem from 'components/ReviewItem'
import TherapistReviewItem from 'components/TherapistReviewItem'
import { getTherapistReviews, getCustomerReviews, resetReviews } from 'containers/Review/actions'
import { getCustomerReviews as getCustomerReviewsService } from 'Services/ReviewService'
import {
  makeSelectReviewList,
  makeSelectIsLoading,
  makeSelectSummaryReview,
  makeSelectReviewPagination,
  makeSelectReviewGroupByRating,
  makeSelectCategorySummary
} from 'containers/Review/selectors'
import messages from './messages'
import './style.scss'

const ReviewTab = ({
  intl,
  user,
  reviews,
  summaryReview,
  reviewGroupByRating,
  isLoading,
  pagination,
  getReviews,
  unsetReviews,
  categorySummary = {}
}) => {
  const [allReview, setAllReview] = useState([])
  const [query, setQuery] = useState({
    page: 1,
    limit: 10
  })

  useEffect(() => {
    user._id && getReviews(user.role, { userId: user._id, ...query })
    return unsetReviews
  }, [])

  useEffect(() => {
    if (user.role === 'therapist' && user._id) {
      const fetchCustomerReviews = async () => {
        try {
          await getCustomerReviewsService({
            userId: user._id,
            page: 1,
            limit: 1000
          })
          // Không cần setCustomerReviews nếu không dùng
        } catch (error) {
          // ignore
        }
      }
      fetchCustomerReviews()
    }
  }, [user._id, user.role])

  useEffect(
    () => {
      setAllReview([...allReview, ...reviews])
    },
    [reviews],
  )

  useEffect(
    () => {
      const data = {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0
      }
      reviewGroupByRating.map(i => (data[i.rating] += i.count))
      return setGroupStars(data)
    },
    [reviewGroupByRating],
  )

  const loadMore = () => {
    const params = {
      page: pagination.page + 1,
      limit: query.limit
    }
    getReviews(user.role, { userId: user._id, ...params })
    setQuery(params)
  }

  // Use categorySummary if available, otherwise calculate from reviews
  const hasGoodCount = categorySummary && categorySummary.overall && categorySummary.overall.GOOD
  const hasBadCount = categorySummary && categorySummary.overall && categorySummary.overall.BAD
  const goodCount = hasGoodCount ? categorySummary.overall.GOOD : allReview.filter(r => r.rating >= 3).length
  const badCount = hasBadCount ? categorySummary.overall.BAD : allReview.filter(r => r.rating > 0 && r.rating < 3).length
  // Total reviews is the sum of good and bad counts
  const totalReviews = goodCount + badCount

  // Calculate groupStars for therapist role rating breakdown
  const groupStars = allReview.reduce((acc, review) => {
    if (review.rating) {
      acc[review.rating] = (acc[review.rating] || 0) + 1
    }
    return acc
  }, {})

  return (
    <section className='ReviewTab row'>
      <article className='col-sm-4'>
        <div className='card'>
          <div className='card-header'>
            {intl.formatMessage(messages.averageRating)}
          </div>
          <div className='card-body'>
            <div className='ReviewTab__averageRating'>
              <strong>
                {summaryReview.sumRating && summaryReview.sumReviewer ? _.round(summaryReview.sumRating / summaryReview.sumReviewer, 1) : 0}
                <i className='fa fa-star' />
              </strong>
              <p>
                {intl.formatMessage(messages.descriptionAverageRating, {
                  sumReviewer: summaryReview.sumReviewer || 0
                })}
              </p>
            </div>
            <ul className='ReviewTab__ratings'>
              {Object.keys(groupStars)
                .sort()
                .reverse()
                .map(i => {
                  const percent = groupStars[i] && summaryReview.sumReviewer ? groupStars[i] / summaryReview.sumReviewer * 100 : 0
                  return (
                    <li key={`li_${i}_${reviews._id}_${new Date().getTime()}`}>
                      <span>
                        {intl.formatMessage(messages.stars)}
                        {' '}
                        {i}
                      </span>
                      <span className='ReviewTab__ratings__process'>
                        <ProgressBar now={percent} />
                      </span>
                      <span>
                        {percent > 0 && '~'}
                        {Math.round(percent)}
                        %
                      </span>
                    </li>
                  )
                })}
              {/* Add customer review statistics for therapists inside ratings */}
              {user.role === 'therapist' && (
                <li className='ReviewTab__subcategory-statistics'>
                  <SubcategoryReviewSummary customerReviews={customerReviews} />
                </li>
              )}
            </ul>
          </div>
        </div>
      </article>
      <article className='col-sm-8'>
        <div className='card ReviewTab__reviews'>
          <div className='card-header'>
            <FormattedMessage {...messages.reviewList} />
          </div>
          <div className='card-body'>
            {allReview.length
              ? allReview.map(review => {
                if (user.role === 'customer') {
                  return (
                    <CustomerReviewItem
                      key={`CustomerReviewItem_${review._id}_${new Date().getTime()}`}
                      review={review}
                      user={user}
                      intl={intl}
                    />
                  )
                }
                // Use TherapistReviewItem for therapist dashboard, ReviewItem for others
                const ReviewComponent = user.role === 'therapist' ? TherapistReviewItem : ReviewItem
                return (
                  <ReviewComponent
                    key={`ReviewItem_${review._id}_${new Date().getTime()}`}
                    review={review}
                    lastPercent
                    user={{ ...review.reviewer, role: user.role === 'therapist' ? 'customer' : 'therapist' }}
                    type='ReviewTab'
                  />
                )
              })
              : !isLoading && <i>{intl.formatMessage(messages.noReview)}</i>}
            <div className='ReviewTab__action'>
              {!isLoading && pagination.page < pagination.lastPage && (
                <button
                  type='button'
                  className='btn btn-light'
                  onClick={loadMore}
                >
                  {intl.formatMessage(messages.loadMore)}
                </button>
              )}
              {isLoading && <i className='fa fa-circle-o-notch fa-spin' />}
            </div>
          </div>
        </div>
      </article>
    </section>
  )
}

ReviewTab.propTypes = {
  intl: intlShape.isRequired,
  isLoading: PropTypes.bool,
  user: PropTypes.object,
  pagination: PropTypes.object,
  reviews: PropTypes.array,
  summaryReview: PropTypes.object,
  reviewGroupByRating: PropTypes.array,
  categorySummary: PropTypes.object,
  getReviews: PropTypes.func,
  unsetReviews: PropTypes.func
}

const mapDispatchToProps = dispatch => ({
  getReviews: (role, query) => dispatch(role === 'therapist' ? getTherapistReviews(query) : getCustomerReviews(query)),
  unsetReviews: () => dispatch(resetReviews())
})
const mapStateToProps = createStructuredSelector({
  reviews: makeSelectReviewList(),
  summaryReview: makeSelectSummaryReview(),
  pagination: makeSelectReviewPagination(),
  isLoading: makeSelectIsLoading(),
  reviewGroupByRating: makeSelectReviewGroupByRating(),
  categorySummary: makeSelectCategorySummary()
})

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
)

export default compose(withConnect, injectIntl)(ReviewTab)

.SubcategoryReviewSummary {
  width: 100%;
  height: 104px;
  position: relative;

  &__header {
    position: absolute;
    left: 24px;
    top: 0;
    width: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  &__spacer {
    height: 28px; // Height of first row (labels)
    padding: 6px 0;
  }

  &__emoji-group {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 6px 0;
    width: 40px;
    height: auto;

    .emoji-icon {
      width: 20px;
      height: 20px;
    }
  }

  &__content {
    position: absolute;
    left: 80px;
    top: 0;
    width: 216px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    gap: 8px;
    padding: 6px 0;
    width: 100%;
  }

  &__cell {
    width: 48px;
    text-align: center;
    font-family: 'Noto Sans JP', sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 1.43;
  }

  // Labels row styling
  &__row:first-child &__cell {
    color: #5F6E7A;
  }

  // Data rows styling (good and bad counts)
  &__row:not(:first-child) &__cell {
    font-weight: 500;
    color: #324452;
  }
} 
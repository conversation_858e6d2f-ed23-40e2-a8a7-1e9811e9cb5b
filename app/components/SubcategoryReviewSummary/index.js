import React from 'react'
import PropTypes from 'prop-types'
import { FormattedMessage, intlShape, injectIntl } from 'react-intl'
import statisticsHappyEmoji from 'assets/img/rating-happy-active.svg'
import statisticsSadEmoji from 'assets/img/rating-sad-active.svg'
import messages from './messages'
import './style.scss'

const SubcategoryReviewSummary = ({ customerReviews, intl }) => {
  // Calculate statistics from customer reviews
  const calculateCategoryStats = () => {
    const stats = {
      technique: { good: 0, bad: 0 },
      service: { good: 0, bad: 0 },
      cost: { good: 0, bad: 0 }
    }

    customerReviews.forEach(review => {
      if (review.categories) {
        if (review.categories.technique === 'GOOD') stats.technique.good += 1
        if (review.categories.technique === 'BAD') stats.technique.bad += 1

        if (review.categories.service === 'GOOD') stats.service.good += 1
        if (review.categories.service === 'BAD') stats.service.bad += 1

        if (review.categories.cost === 'GOOD') stats.cost.good += 1
        if (review.categories.cost === 'BAD') stats.cost.bad += 1
      }
    })

    return stats
  }

  const stats = calculateCategoryStats()

  return (
    <div className='SubcategoryReviewSummary'>
      <div className='SubcategoryReviewSummary__header'>
        <div className='SubcategoryReviewSummary__spacer'></div>
        <div className='SubcategoryReviewSummary__emoji-group'>
          <img src={statisticsHappyEmoji} alt='Happy' className='emoji-icon' />
        </div>
        <div className='SubcategoryReviewSummary__emoji-group'>
          <img src={statisticsSadEmoji} alt='Sad' className='emoji-icon' />
        </div>
      </div>

      <div className='SubcategoryReviewSummary__content'>
        {/* Labels row */}
        <div className='SubcategoryReviewSummary__row'>
          <div className='SubcategoryReviewSummary__cell'>
            <FormattedMessage {...messages.technique} />
          </div>
          <div className='SubcategoryReviewSummary__cell'>
            <FormattedMessage {...messages.service} />
          </div>
          <div className='SubcategoryReviewSummary__cell'>
            <FormattedMessage {...messages.cost} />
          </div>
        </div>

        {/* Good counts row */}
        <div className='SubcategoryReviewSummary__row'>
          <div className='SubcategoryReviewSummary__cell'>
            {stats.technique.good}
            件
          </div>
          <div className='SubcategoryReviewSummary__cell'>
            {stats.service.good}
            件
          </div>
          <div className='SubcategoryReviewSummary__cell'>
            {stats.cost.good}
            件
          </div>
        </div>

        {/* Bad counts row */}
        <div className='SubcategoryReviewSummary__row'>
          <div className='SubcategoryReviewSummary__cell'>
            {stats.technique.bad}
            件
          </div>
          <div className='SubcategoryReviewSummary__cell'>
            {stats.service.bad}
            件
          </div>
          <div className='SubcategoryReviewSummary__cell'>
            {stats.cost.bad}
            件
          </div>
        </div>
      </div>
    </div>
  )
}

SubcategoryReviewSummary.propTypes = {
  customerReviews: PropTypes.array.isRequired,
  intl: intlShape.isRequired
}

export default injectIntl(SubcategoryReviewSummary)

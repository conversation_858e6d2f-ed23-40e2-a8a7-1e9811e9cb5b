import React, { useState, useRef, useEffect } from 'react'
import PropTypes from 'prop-types'
import iconArrowDown from 'assets/img/arrow-down.svg'
import iconSort from 'assets/img/booking/sort.svg'

import './style.scss'

const SortingButtons = ({
  currentSort,
  currentOrder,
  onSortChange,
  onOrderChange,
  onSortAndOrderChange,
  className = ''
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef(null)
  const hoverTimeoutRef = useRef(null)

  // Get current selection text
  const getCurrentSelectionText = () => {
    // Check for exact combination matches
    if ((currentSort === 'bookingTime' || currentSort === 'dateBooking') && currentOrder === 'desc') {
      return '予約日時順(新しい順)'
    }
    if ((currentSort === 'requestTime' || currentSort === 'createdAt') && currentOrder === 'asc') {
      return '予約リクエスト送信日時順(古い順)'
    }
    // Default fallback
    return '予約日時順(新しい順)'
  }

  // Fixed dropdown options
  const getDropdownOptions = () => [
    {
      sortValue: 'bookingTime',
      orderValue: 'desc',
      displayText: '予約日時順(新しい順)',
      isActive: (currentSort === 'bookingTime' || currentSort === 'dateBooking') && currentOrder === 'desc'
    },
    {
      sortValue: 'requestTime',
      orderValue: 'asc',
      displayText: '予約リクエスト送信日時順(古い順)',
      isActive: (currentSort === 'requestTime' || currentSort === 'createdAt') && currentOrder === 'asc'
    }
  ]

  const handleOptionClick = (sortValue, orderValue) => {
    // Use the new combined method if available, otherwise fallback to separate calls
    if (onSortAndOrderChange) {
      onSortAndOrderChange(sortValue, orderValue)
    } else {
      onSortChange(sortValue)
      onOrderChange(orderValue)
    }
    setIsDropdownOpen(false)
  }

  const handleMouseEnter = () => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }
    setIsDropdownOpen(true)
  }

  const handleMouseLeave = () => {
    hoverTimeoutRef.current = setTimeout(() => {
      setIsDropdownOpen(false)
    }, 200) // 200ms delay to allow moving to dropdown
  }

  // Cleanup timeout on unmount
  useEffect(() => () => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }
  }, [])

  return (
    <div className={`sorting-buttons ${className}`} ref={dropdownRef}>
      <div className='sorting-buttons-container'>
        {/* Sorting Area with Hover */}
        <div
          className={`sorting-dropdown-trigger ${isDropdownOpen ? 'active' : ''}`}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {/* Sort Icon - using sort.svg */}
          <div className='sorting-icon'>
            <img src={iconSort} />
          </div>

          <span className='sorting-current-selection'>
            {getCurrentSelectionText()}
          </span>

          {/* Dropdown Arrow */}
          <div className='sorting-dropdown-arrow'>
            <img src={iconArrowDown} />
          </div>
        </div>

        {/* Dropdown Modal */}
        <div
          className={`sorting-dropdown-modal ${isDropdownOpen ? 'visible' : ''}`}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {getDropdownOptions().map((option, index) => (
            <div
              key={index}
              className={`sorting-modal-option ${option.isActive ? 'active' : ''}`}
              onClick={() => handleOptionClick(option.sortValue, option.orderValue)}
            >
              <span className='sorting-modal-option-text'>
                {option.displayText}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

SortingButtons.propTypes = {
  currentSort: PropTypes.string.isRequired,
  currentOrder: PropTypes.string.isRequired,
  onSortChange: PropTypes.func.isRequired,
  onOrderChange: PropTypes.func.isRequired,
  onSortAndOrderChange: PropTypes.func,
  className: PropTypes.string
}

export default SortingButtons

// Sorting Dropdown Component - Hover behavior with sort.svg icon
.sorting-buttons {
  margin-top: 12px;
  margin-bottom: 16px;
  padding: 0 10px;
  position: relative;
}

.sorting-buttons-container {
  display: flex;
  align-items: center;
  position: relative;
}

// Main dropdown trigger area - hover to show dropdown
.sorting-dropdown-trigger {
  display: flex;
  align-items: center;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
  position: relative;

  &.active {
    border-color: #4e73df;
    box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.1);
  }
}

// Sort icon using sort.svg
.sorting-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  color: #818F9A;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// Current selection text
.sorting-current-selection {
  flex: 1;
  font-size: 14px;
  color: #495057;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Dropdown arrow icon
.sorting-dropdown-arrow {
  margin-left: 8px;
  transition: transform 0.2s ease;

  svg {
    width: 100%;
    height: 100%;
    fill: #6c757d;
  }
}

// Dropdown modal - appears on hover
.sorting-dropdown-modal {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 2px;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-8px);
  transition: all 0.15s ease;
  max-width: 256px;

  &.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

// Modal option items
.sorting-modal-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f8f9fa;
  }

  &.active {
    background: #e7f3ff;
    color: #4e73df;
    font-weight: 500;

    &::after {
      content: '✓';
      margin-left: auto;
      color: #4e73df;
      font-weight: bold;
    }
  }
}

.sorting-modal-option-text {
  font-size: 14px;
  color: #324452;

  .sorting-modal-option.active & {
    color: #324452;
  }
}

// Booking specific styling to match the design
.booking-sorting-buttons {
  // Remove label since we're not using it in the new design
  .sorting-dropdown-trigger {
    color: #324452;
    font-size: 14px;

    // &:hover {
    //   background: #f8f9fc;
    //   border-color: #d1d3e2;
    // }

    &.active {
      border-color: #4e73df;
      box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.1);
    }
  }

  .sorting-current-selection {
    color: #5a5c69;
    font-weight: 400;
  }

  .sorting-dropdown-arrow svg {
    fill: #324452;
  }

  .sorting-modal-option {
    font-size: 14px;

    &.active {
      background: #E4EBEF;
      color: #324452;
      font-weight: 500;

      &::after {
        color: #324452;
      }
    }
  }

  .sorting-modal-option-text {
    color: #5a5c69;

    .sorting-modal-option.active & {
      color: #4e73df;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .sorting-buttons-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .sorting-label {
    margin-right: 0;
    margin-bottom: 4px;
  }

  .sorting-dropdown-trigger {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .sorting-dropdown-trigger {
    padding: 10px 12px;
    font-size: 13px;
  }

  .sorting-modal-option {
    padding: 14px 16px;
  }

  .sorting-modal-option-text {
    font-size: 13px;
  }
}

.order-separator {
  color: #dee2e6;
  font-size: 14px;
  margin: 0 4px;
}

.booking-sorting-buttons {
  .sorting-label {
    color: #5a5c69;
    font-size: 14px;
  }
  
  .sorting-button {
    border-color: #e3e6f0;
    color: #5a5c69;
    
    &:hover {
      background: #f8f9fc;
      border-color: #d1d3e2;
    }
    
    &.active {
      background: #4e73df;
      border-color: #4e73df;
      color: #ffffff;
      
      &:hover {
        background: #2e59d9;
        border-color: #2e59d9;
      }
    }
  }
  
  .sorting-order-button {
    color: #858796;
    
    &:hover {
      color: #5a5c69;
    }
    
    &.active {
      color: #4e73df;
    }
  }
  
  .order-separator {
    color: #d1d3e2;
  }
}

// Responsive design
@media (max-width: 768px) {
  .sorting-buttons-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .sorting-label {
    margin-right: 0;
    margin-bottom: 4px;
  }
  
  .sorting-options {
    width: 100%;
    
    .sorting-button {
      flex: 1;
      min-width: 0;
      padding: 10px 12px;
      font-size: 13px;
    }
  }
  
  .sorting-order-options {
    margin-left: 0;
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .sorting-options {
    flex-direction: column;
    
    .sorting-button {
      width: 100%;
      text-align: center;
    }
  }
  
  .sorting-order-options {
    flex-direction: row;
    justify-content: center;
  }
}

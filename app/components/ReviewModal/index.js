import React, { useEffect } from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import { compose } from 'redux'
import { createStructuredSelector } from 'reselect'
import { Modal } from 'react-bootstrap'
import { FormattedMessage } from 'react-intl'
import moment from 'moment-timezone'
import { getBookingReviews, resetReviews } from 'containers/Review/actions'
import {
  makeSelectReviewList,
  makeSelectIsLoading
} from 'containers/Review/selectors'
import ReviewItem from 'components/ReviewItem'
import messages from './messages'
import './style.scss'

const ReviewModal = ({
  show,
  onHide,
  reviews = [],
  bookingId,
  getReviews,
  isLoading,
  unsetReviews,
  ...users
}) => {
  useEffect(() => unsetReviews, [])

  useEffect(
    () => {
      show && bookingId && getReviews(bookingId)
    },
    [show],
  )

  return (
    <Modal
      show={show}
      onHide={onHide}
      backdrop='static'
      className='ReviewModal'
    >
      <Modal.Header>
        <Modal.Title>
          <FormattedMessage {...messages.title} />
        </Modal.Title>
        <button
          type='button'
          className='close'
          onClick={onHide}
          aria-label='Close'
        >
          <svg width='16' height='16' viewBox='0 0 16 16' fill='none'>
            <path
              fillRule='evenodd'
              clipRule='evenodd'
              d='M8.93934 8L13.4697 3.46967C13.7626 3.17678 13.7626 2.70191 13.4697 2.40901C13.1768 2.11612 12.7019 2.11612 12.409 2.40901L7.87868 6.93934L3.34835 2.40901C3.05546 2.11612 2.58058 2.11612 2.28769 2.40901C1.9948 2.70191 1.9948 3.17678 2.28769 3.46967L6.81802 8L2.28769 12.5303C1.9948 12.8232 1.9948 13.2981 2.28769 13.591C2.58058 13.8839 3.05546 13.8839 3.34835 13.591L7.87868 9.06066L12.409 13.591C12.7019 13.8839 13.1768 13.8839 13.4697 13.591C13.7626 13.2981 13.7626 12.8232 13.4697 12.5303L8.93934 8Z'
              fill='#608099'
            />
          </svg>
        </button>
      </Modal.Header>
      <Modal.Body>
        {isLoading ? (
          <div className='ReviewModal__loading'>
            <i className='fa fa-circle-o-notch fa-spin fa-2x' />
          </div>
        ) : (
          Object.keys(users).map(role => {
            const review = reviews.find(i => i.reviewer.role === role)
            const user = { ...users[role], role }
            return (
              <ReviewItem
                key={`${role}_${moment.now()}`}
                review={review || { bookingId }}
                user={user}
                type='ReviewModal'
              />
            )
          })
        )}
      </Modal.Body>
    </Modal>
  )
}

ReviewModal.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  isLoading: PropTypes.bool,
  reviews: PropTypes.array,
  bookingId: PropTypes.string,
  therapist: PropTypes.object,
  customer: PropTypes.object,
  getReviews: PropTypes.func,
  unsetReviews: PropTypes.func
}

const mapDispatchToProps = dispatch => ({
  getReviews: bookingId => dispatch(getBookingReviews(bookingId)),
  unsetReviews: () => dispatch(resetReviews())
})
const mapStateToProps = createStructuredSelector({
  reviews: makeSelectReviewList(),
  isLoading: makeSelectIsLoading()
})

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
)

export default compose(withConnect)(ReviewModal)

.ReviewModal {
  .modal-dialog {
    max-width: 560px;
    margin: 2rem auto;
  }

  .modal-content {
    border-radius: 4px;
    border: none;
    box-shadow: 0px 8px 32px rgba(0, 0, 0, 0.1);
    padding: 0;
  }

  .modal-header {
    background: linear-gradient(135deg, rgba(255, 210, 128, 0.2) 0%, rgba(255, 240, 212, 0.2) 27.79%, rgba(255, 246, 229, 0.2) 49.67%, rgba(204, 233, 255, 0.2) 75.38%, rgba(107, 170, 220, 0.2) 100%);
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    padding: 26px 24px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    .modal-title {
      font-family: 'Noto Sans JP', sans-serif;
      font-size: 20px;
      font-weight: 500;
      line-height: 1.448;
      color: #324452;
      margin: 0;
      position: absolute;
      left: 24px;
      top: 50%;
      transform: translateY(-50%);
    }

    .close {
      width: 24px;
      height: 24px;
      padding: 0;
      margin: 0;
      border: none;
      background: none;
      color: #608099;
      opacity: 1;
      text-shadow: none;
      outline: none;
      position: absolute;
      right: 24px;
      top: 50%;
      transform: translateY(-50%);
      
      &:hover {
        color: #324452;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .modal-body {
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    color: #608099;
  }
}

.ReviewItem {
  width: 528px;
  background: #EBEEF0;
  border-radius: 4px;
  padding: 16px 16px 24px;
  margin: 0;
  border: none;

  &:not(:last-child) {
    margin-bottom: 0;
  }

  .media {
    display: flex;
    align-items: flex-start;
    gap: 8px;
  }

  .thumbnail {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid #FFFFFF;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.16);
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
    margin-right: 0;
  }

  .media-body {
    flex: 1;
    min-width: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  &__user {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;

    .name-reviewer {
      font-family: 'Noto Sans JP', sans-serif;
      font-size: 14px;
      font-weight: 500;
      color: #324452;
      margin: 0;
      overflow: hidden;
      max-width: 250px;
      white-space: nowrap;
    }

    .badge {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
      background: #DAE1E7;
      color: #5F6E7A;
      border: none;
      display: inline-block;
      margin-top: 4px;
      text-decoration: none;

      &:hover {
        background: #C1C9D1;
        text-decoration: none;
        color: #5F6E7A;
      }
    }
  }

  &__date {
    font-size: 12px;
    color: #A7B5C0;
    margin-left: auto;
  }

  &__group-action {
    display: flex;
    gap: 8px;
    align-items: center;

    .btn {
      width: 24px;
      height: 24px;
      padding: 4px;
      border: 1px solid #DAE1E7;
      border-radius: 4px;
      background: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: #A7B5C0;
      }

      i {
        font-size: 12px;
        margin: 0;
      }

      &.text-primary i {
        color: #5F93FB;
      }

      &.text-danger i {
        color: #FF3B3B;
      }
    }
  }

  &__rating {
    display: flex;
    gap: 2px;
    margin-top: 8px;

    i {
      font-size: 14px;
      color: #FFB300;

      &.fa-star-o {
        color: #DAE1E7;
      }
    }
  }

  &__content {
    font-family: 'Noto Sans JP', sans-serif;
    font-size: 14px;
    line-height: 1.448;
    color: #324452;
    margin-top: 16px;

    dl {
      margin: 0;

      dt {
        font-weight: 500;
        margin-bottom: 4px;
        color: #5F6E7A;
      }

      dd {
        margin-bottom: 12px;
        margin-left: 0;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

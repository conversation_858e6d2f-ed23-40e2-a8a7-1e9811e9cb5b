{"global.save": "保存する", "global.saveActivate": "保存してアクティブ", "global.cancel": "キャンセル", "global.confirm": "確認", "global.close": "いいえ", "global.remove": "削除", "global.yes": "はい", "global.titleCloseModal": "入力は保存されません", "global.messageCloseModal": "ここまでの入力は保存されていません。ポップアップを閉じてよろしいですか？", "global.validation.required": "この項目は必須です", "global.validation.maxLength": "このフィールドは長すぎます（最大は{max}文字です）。", "global.phone": "電話番号", "global.validation.phone": "9〜12の数字から入力してください", "global.validation.notSpecialCharacter": "特殊文字を削除してください", "global.validation.japaneseText": "特殊文字を削除してください", "global.validation.email": "メールが無効です", "global.validation.notMatch": "パスワードが一致しません", "global.validation.invalidPassword": "パスワードは最低でも6文字必要です", "global.ageRange": "{ageRange}才", "global.overAgeRange": "{ageRange}才以上", "global.customer": "名", "global.booking": "件", "global.login": "ログイン", "global.signup": "新規登録", "global.done": "完了", "global.edit": "編集", "global.search": "検索", "global.notification": "お知らせ", "global.noOptions": "オプションなし", "global.select": "選択してください...", "global.items": "アイテム", "global.administrator": "管理者", "global.admin": "管理者", "global.operator": "オペレーター", "global.cash": "現金", "global.card": "カード", "global.gcard": "カード", "global.time": "時間", "global.loadingOptions": "積み込み...", "global.searchKeyword": "検索語", "global.apply": "適用する", "global.clear": "クリアー", "global.hours": "時", "global.minutes": "分", "global.ALL": "全て", "global.PAID": "支払い済", "global.UNPAID": "未払い", "global.IN_PROCESS": "処理中", "global.CYCLE_TRANSFER": "次回繰越", "global.changeStatusFailed": "ステータスを変更できませんでした", "global.markpaid": "支払い完了チェック", "global.view": "詳細をみる", "global.to": "から", "global.role.therapist": "セラピスト", "global.role.customer": "お客様", "global.role.agent": "エージェント", "global.role.operator": "オペレーター", "global.role.administrator": "管理者", "global.role.system": "システム", "global.anonymous": "エリア外の", "global.messageConfirmEditModal": "この情報を変更してもよろしいですか?", "global.messageConfirmBlockUserModal": "このユーザーをブロックしてもよろしいですか?", "global.messageConfirmRemoveUserModal": "本当に削除してもよろしいでしょうか？", "global.block": "ブロック", "global.unblock": "ブロックを解除", "global.createUnload": "別のページに移動すると、入力した情報は失われます。", "global.editUnload": "別のページに移動すると、編集した情報は失われます。", "global.messageInternalError": "内部サーバーエラー", "global.comment": "メモ ・共有事項", "global.schedule": "スケジュール", "global.date": "日にち", "global.new": "New", "global.gender.any": "どちらでも", "global.gender.male": "男性", "global.gender.female": "女性", "global.btn.save": "保存する", "global.btn.cancel": "キャンセル", "app.containers.HomePage.header": "特殊文字ではなく日本語または英語のテキストを入力してください", "app.containers.NotFoundPage.header": "", "app.components.AccountForm.username": "ユーザー名（ログイン時に使用します）", "app.components.AccountForm.validate.username": "ユーザー名を入力してください", "app.components.AccountForm.name": "氏名", "app.components.AccountForm.validate.name": "名前を入力してください", "app.components.AccountForm.password": "パスワード", "app.components.AccountForm.validate.password": "パスワードを入力してください", "app.components.AccountForm.email": "Eメール", "app.components.AccountForm.validate.email.required": "メールアドレスを入力してください", "app.components.AccountForm.validate.email.invalid": "メールが無効です", "app.components.AccountForm.phone": "電話番号", "app.components.AccountForm.validate.phone": "有効な電話番号を入力して下さい", "app.components.AccountForm.role": "権限", "app.components.AccountForm.validate.role": "役割を選択してください", "app.components.AccountForm.usergroup": "都道府県", "app.components.AccountForm.validate.usergroup": "ユーザーグループを選択してください", "app.containers.AccountAdmin.header": "FCアカウント管理", "app.containers.AccountAdmin.table.add": "FCアカウントを作成", "app.containers.AccountAdmin.table.id": "#ID", "app.containers.AccountAdmin.table.lastLogin": "最終ログイン", "app.containers.AccountAdmin.table.createdAt": "アカウント作成日", "app.containers.AccountAdmin.table.name": "氏名", "app.containers.AccountAdmin.table.username": "ユーザーネーム", "app.containers.AccountAdmin.table.email": "Eメール", "app.containers.AccountAdmin.table.phone": "電話番号", "app.containers.AccountAdmin.table.role": "権限", "app.containers.AccountAdmin.table.branches": "都道府県", "app.containers.AccountAdmin.table.status": "ステータス", "app.containers.AccountAdmin.table.edit": "編集", "app.containers.AccountAdmin.table.active": "活動的", "app.containers.AccountAdmin.table.inactive": "非アクティブ", "app.containers.AccountAdmin.status.active": "アクティブなアカウント", "app.containers.AccountAdmin.status.inactive": "利用停止アカウント", "app.containers.AccountAdmin.addingModelTitle": "新規FCアカウントを作成", "app.containers.BookingMenu.table.add": "新規メニュー登録", "app.containers.BookingMenu.table.id": "メニューID", "app.containers.BookingMenu.table.title": "メニュー名", "app.containers.BookingMenu.table.price": "価格", "app.containers.BookingMenu.table.taxed": "課税", "app.containers.BookingMenu.table.public": "パブリック", "app.containers.BookingMenu.table.unPublic": "未公開", "app.containers.BookingMenu.table.branches": "支店所在地", "app.containers.BookingMenu.table.status": "ステータス", "app.containers.BookingMenu.table.edit": "編集", "app.containers.BookingMenu.table.active": "アクティブ", "app.containers.BookingMenu.table.inactive": "休止中", "app.components.Sidebar.Overview": "概要", "app.components.Sidebar.Booking": "予約管理", "app.components.Sidebar.agent": "エージェント管理", "app.components.Sidebar.Admin": "FCアカウント管理", "app.components.Sidebar.Therapist": "セラピスト管理", "app.components.Sidebar.Menu": "施術メニュー", "app.components.Sidebar.Schedule": "スケジュール", "app.components.Sidebar.Sales": "売上高", "app.components.Sidebar.Configuration": "設定", "app.components.Sidebar.Payout": "支払い管理", "app.components.Sidebar.PaymentRequest": "即日払い管理", "app.components.Sidebar.Customer": "お客様管理", "app.components.Sidebar.Marketing": "マーケティングスタジオ", "app.components.Sidebar.Coupon": "クーポン管理", "app.components.Sidebar.Announcement": "アナウンス管理", "app.components.Sidebar.Feedback": "サービス評価", "app.components.Sidebar.Review": "レビュー管理", "app.components.Sidebar.Chat": "チャット履歴", "app.components.Sidebar.therapistPayout": "セラピスト", "app.components.Sidebar.salonPayout": "サロン", "app.components.Sidebar.agentPayout": "エージェント", "app.containers.Booking.type": "タイプ", "app.containers.Booking.APPOINTMENT": "指名予約", "app.containers.Booking.ONDEMAND": "今すぐ予約", "app.containers.Booking.STANDARD": "標準", "app.containers.Booking.NOW": "標準", "app.containers.Booking.header": "予約管理", "app.containers.Booking.bookingId": "#ID", "app.containers.Booking.therapistName": "セラピスト", "app.containers.Booking.customerName": "お客様名", "app.containers.Booking.phoneNumber": "電話番号", "app.containers.Booking.dateBooking": "送信日時", "app.containers.Booking.arrivalTime": "到着予定時刻", "app.containers.Booking.treatmentTime": "予約日時", "app.containers.Booking.finishTime": "終了時間", "app.containers.Booking.address": "都道府県", "app.containers.Booking.status": "ステータス", "app.containers.Booking.requestStatus": "予約リクエスト", "app.containers.Booking.confirmedStatus": "仮予約/予約完了", "app.containers.Booking.doneStatus": "お会計完了", "app.containers.Booking.cancelStatus": "キャンセル", "app.containers.Booking.assign": "セラピストを指名", "app.containers.Booking.amount": "決済金額", "app.containers.Booking.tooltipCancelPaidSuccess": "キャンセル料の決済が正常に行われました", "app.containers.Booking.tooltipCancelPaidFailed": "決済はまだ完了していません", "app.containers.Booking.tooltipDonePaidSuccess": "決済は正常に行われました", "app.containers.Booking.bookingNotExists": "あなたが見つけた予約はありません。 予約IDを再確認してください", "app.containers.Booking.rightNow": "今すぐ", "app.components.BookingBadges.reserve": "{value}予約", "app.components.BookingBadges.REQUEST": "リクエスト中", "app.components.BookingBadges.ARRIVED": "施術中", "app.components.BookingBadges.CONFIRMED": "確定予約", "app.components.BookingBadges.DONE": "お会計完了", "app.components.BookingBadges.CANCELED": "キャンセル", "app.components.BookingBadges.THERAPIST_CANCELED": "セラピキャンセル", "app.components.BookingStatus.NEW": "リクエスト中", "app.components.BookingStatus.THERAPIST_CANCELED": "セラピキャンセル", "app.components.BookingStatus.CANCELED": "キャンセル", "app.components.BookingStatus.DONE": "施術完了", "app.components.BookingStatus.IN_TRANSIT": "到着時刻OK", "app.components.BookingStatus.ARRIVED": "施術中", "app.components.BookingStatus.CONFIRMED": "仮予約", "app.components.BookingStatus.DEFAULT_STATUS": "デフォルトのステータステキスト", "app.components.BookingStatus.PENDING": "返答待ち", "app.containers.BookingDetail.customerName": "お客様名", "app.containers.BookingDetail.customerPhoneNumber": "電話番号", "app.containers.BookingDetail.customerGender": "お客様の性別", "app.containers.BookingDetail.dateBooking": "送信日時", "app.containers.BookingDetail.treatmentTime": "予約日時", "app.containers.BookingDetail.duration": "施術時間", "app.containers.BookingDetail.minutes": " 分", "app.containers.BookingDetail.address": "住所", "app.containers.BookingDetail.prefecture": "都道府県", "app.containers.BookingDetail.point": "付与セラピストポイント", "app.containers.BookingDetail.commission": "予約手数料", "app.containers.BookingDetail.therapistGender": "セラピストの性別", "app.containers.BookingDetail.menuLabel": "メニュー", "app.containers.BookingDetail.couponLabel": "クーポン値引", "app.containers.BookingDetail.midnightLabel": "深夜料金", "app.containers.BookingDetail.total": "合計", "app.containers.BookingDetail.parkingNote": "訪問先について", "app.containers.BookingDetail.customerNote": "施術内容について", "app.containers.BookingDetail.therapist": "セラピスト", "app.containers.BookingDetail.therapistPhone": "セラピストの電話番号", "app.containers.BookingDetail.titleFirstStep": "1. 施術内容", "app.containers.BookingDetail.titleSecondStep": "2. 日時と場所", "app.containers.BookingDetail.titleThirdStep": "3. お客様情報", "app.containers.BookingDetail.titleConfirmCompleteBookingModal": "予約完了", "app.containers.BookingDetail.messageConfirmCompleteBookingModal": "この予約を完了しますか？ その場合、この予約は現金による支払いタイプで完了します。", "app.containers.BookingDetail.titleChangeReasonBookingModal": "確認", "app.containers.BookingDetail.messageChangeReasonBookingModal": "ステータスを[通常決済]に変更します。よろしいですか？", "app.containers.BookingDetail.bookingPaidDone": "支払い完了", "app.containers.BookingDetail.bookingPaidDoneNotTreament": "未施術での決済完了", "app.containers.BookingDetail.bookingChangeReasonTreament": "通常決済に変更", "app.containers.BookingDetail.bookingPaidError": "決済エラー", "app.containers.BookingDetail.messageTooltipChangePaymentStatus": "支払いを完了としてマークする", "app.containers.BookingDetail.messageModalChangePaymentStatusContent": "この顧客の支払いを完了しましたか？", "app.containers.BookingDetail.messageModalChangePaymentStatusNote": "注：この予約の支払い方法は「現金」に更新されます。", "app.containers.BookingDetail.messageModalChangePaymentStatusTittle": "確認", "app.containers.BookingDetail.penalty": "ペナルティ", "app.containers.BookingDetail.edit": "編集", "app.containers.BookingDetail.therapistPoint": "セラピストポイント", "app.containers.BookingDetail.cancelCount": "確定予約キャンセル数", "app.containers.BookingDetail.earnedPointMessage": "獲得ポイント数", "app.components.BookingStatusHistory.header": "進行状況", "app.components.BookingStatusHistory.timeline.REQUEST": "予約を作成しました", "app.components.BookingStatusHistory.timeline.NEW": "予約を作成しました", "app.components.BookingStatusHistory.timeline.PENDING": "予約を作成しました", "app.components.BookingStatusHistory.timeline.CONFIRMED": "予約が成立しました", "app.components.BookingStatusHistory.timeline.IN_TRANSIT": "到着予定時刻が設定されました", "app.components.BookingStatusHistory.timeline.ARRIVED": "施術が開始されました", "app.components.BookingStatusHistory.timeline.CLOSED": "施術完了", "app.components.BookingStatusHistory.timeline.DONE": "施術完了", "app.components.BookingStatusHistory.timeline.DONE_NOT_TREAMENT": "決済完了", "app.components.BookingStatusHistory.timeline.CANCELED": "予約は{role}によってキャンセルされました", "app.components.BookingStatusHistory.timeline.therapistDenyBooking": "予約リクエストはセラピストによってキャンセルされました", "app.components.BookingStatusHistory.timeline.therapistCancelConfirmedBooking": "確定予約はセラピストによってキャンセルされました", "app.components.Summary.man": "男性", "app.components.Summary.menuLabel": "メニュー", "app.components.Summary.total": "合計", "app.components.Summary.minutes": " 分", "app.components.Summary.any": "どちらでも", "app.components.Summary.woman": "女性", "app.components.Summary.extensions": "延長", "app.components.Summary.payWith": "と支払う", "app.components.Summary.midnightLabel": "深夜料金", "app.components.Summary.pointLabel": "ポイント使用", "app.components.Summary.refundPointMessage": "使用されたポイントが返済しました", "app.containers.CreateBooking.addBooking": "新規予約作成", "app.containers.CreateBooking.header": "新規予約を作成", "app.containers.CreateBooking.titleInfoModal": "確認", "app.containers.CreateBooking.messageInfoModal": "仮予約の作成が完了しました！予約ID：#{bookingId}", "app.containers.CreateBooking.titleCloseModal": "確認", "app.containers.CreateBooking.contentCloseModal": "作成中の予約は保存されません。よろしいですか？", "app.components.Booking.firstStep": "施術内容", "app.components.Booking.secondStep": "日時と場所", "app.components.Booking.thirdStep": "お客様情報", "app.components.Booking.any": "どちらでも", "app.components.Booking.man": "男性", "app.components.Booking.woman": "女性", "app.containers.BookingForm.menu": "メニュー", "app.containers.BookingForm.dateBooking": "日時", "app.containers.BookingForm.prefecture": "都道府県", "app.containers.BookingForm.city": "エリア", "app.containers.BookingForm.ward": "エリア（詳細）", "app.containers.BookingForm.district": "地域", "app.containers.BookingForm.address": "番地など", "app.containers.BookingForm.parkingNote": "パーキングメモ (任意)", "app.containers.BookingForm.customerName": "お客様名", "app.containers.BookingForm.customerPhoneNumber": "電話番号", "app.containers.BookingForm.customerGender": "お客様の性別", "app.containers.BookingForm.customerNote": "お客様メモ (任意)", "app.containers.BookingForm.next": "次へ", "app.containers.BookingForm.back": "戻る", "app.containers.BookingForm.createAndAssignBooking": "セラピストの指名へ", "app.containers.BookingForm.createBooking": "保存する", "app.containers.BookingForm.therapistGender": "セラピストの性別", "app.containers.BookingForm.requireMenu": "以下のメニューからお選びください", "app.containers.BookingForm.minimumDuration": "最短期間は60分です", "app.containers.BookingForm.requireCustomerName": "顧客名は2から30文字でなければなりません", "app.containers.Agent.header": "エージェント管理", "app.containers.Agent.add": "アカウントを作成", "app.containers.Agent.id": "ID", "app.containers.Agent.lastLogin": "最終ログイン", "app.containers.Agent.fullName": "エージェント名", "app.containers.Agent.firstName": "名", "app.containers.Agent.lastName": "姓", "app.containers.Agent.name": "名前", "app.containers.Agent.email": "Eメール", "app.containers.Agent.phone": "電話番号", "app.containers.Agent.gender": "性別", "app.containers.Agent.gender.male": "男性", "app.containers.Agent.gender.female": "女性", "app.containers.Agent.gender.other": "どちらでも", "app.containers.Agent.birthday": "生年月日", "app.containers.Agent.companyName": "会社名", "app.containers.Agent.status": "ステータス", "app.containers.Agent.connect": "連携開始", "app.containers.Agent.disconnect": "連携解除", "app.containers.Agent.messageConfirmUnlinkModal": "エージェントとの連携解除をしますか?\n「はい」を選択後、すぐに解除されます。", "app.containers.Agent.linkSuccess": "協力的", "app.containers.Agent.unlinkSuccess": "非協力的成功", "app.containers.Agent.linkFailed": "エージェントからリンクできません", "app.containers.Agent.unlinkFailed": "エージェントとの連携解除ができません", "app.components.AgentLinkingModal.header": "連携するエージェント", "app.containers.AgentDetailPage.header": "プロフィール", "app.containers.AgentDetailPage.profile": "プロフィール", "app.containers.AgentDetailPage.bank": "銀行口座情報", "app.containers.AgentDetailPage.earnings": "アクティブな期間", "app.containers.AgentDetailPage.payouts": "お支払いリスト", "app.components.AgentForm.header": "アカウントを作成", "app.components.AgentForm.validate.firstName": "この項目は必須です", "app.components.AgentForm.validate.lastName": "この項目は必須です", "app.components.AgentForm.validate.gender": "この項目は必須です", "app.components.AgentForm.validate.birthday": "この項目は必須です", "app.components.AgentForm.validate.email.required": "この項目は必須です", "app.components.AgentForm.validate.email.invalid": "メールが無効です", "app.components.AgentForm.validate.phone.required": "この項目は必須です", "app.components.AgentForm.validate.phone.invalid": "9〜12の数字から入力してください", "app.components.AgentForm.save": "保存する", "app.components.AgentEditForm.header": "アカウントを編集", "app.components.AgentDetail.header": "プロフィール", "app.components.AgentDetail.info": "基本情報", "app.components.AgentDetail.lastActitvity": "最終ログイン", "app.components.AgentDetail.lastLogin": "最終ログイン", "app.components.AgentDetail.registerAt": "アカウント作成日", "app.containers.AgentProfile.header": "プロフィール", "app.containers.AgentProfile.bankInfo": "銀行口座情報", "app.containers.AgentProfile.bankRegister": "銀行口座情報登録", "app.containers.AgentProfile.bankName": "金融機関名", "app.containers.AgentProfile.branchName": "支店名", "app.containers.AgentProfile.branchCode": "支店コード", "app.containers.AgentProfile.cardType": "口座種別", "app.containers.AgentProfile.cardNumber": "口座番号", "app.containers.AgentProfile.accountName": "口座名義", "app.components.AgentBankForm.header": "銀行口座を", "app.components.AgentBankForm.save": "保存する", "app.components.AgentBankForm.noteCardNumber": "7桁の口座番号を記載（ゆうちょ銀行の際は他行からの振込口座情報となります）", "app.components.AgentBankForm.validate.required": "この項目は必須です", "app.components.AgentBankForm.validate.cardNumber": "7桁の口座番号を記載", "app.components.AgentBankForm.validate.cardName": "カタカナで入力してください", "app.containers.Therapist.nickName": "登録名", "app.containers.Therapist.fullNameTherapist": "セラピスト名", "app.containers.Therapist.fullName": "お名前", "app.containers.Therapist.agent": "エージェント", "app.containers.Therapist.phone": "電話番号", "app.containers.Therapist.therapistId": "#ID", "app.containers.Therapist.addTherapist": "セラピストアカウントを作成", "app.containers.Therapist.header": "セラピスト管理", "app.containers.Therapist.gender": "性別", "app.containers.Therapist.any": "どちらでも", "app.containers.Therapist.man": "男性", "app.containers.Therapist.woman": "女性", "app.containers.Therapist.email": "Eメール", "app.containers.Therapist.Branches": "Base FC", "app.containers.Therapist.birthday": "生年月日", "app.containers.Therapist.experience": "マッサージ歴", "app.containers.Therapist.menus": "対応可能メニュー", "app.containers.Therapist.ranking": "ランク", "app.containers.Therapist.currentRank": "現在所有セラピストポイント:", "app.containers.Therapist.certificate": "自己PR", "app.containers.Therapist.certificateNote": "(保有資格や経歴など)", "app.containers.Therapist.areaCodes": "対応可能エリア", "app.containers.Therapist.transportation": "移動手段", "app.containers.Therapist.lastLogin": "最終ログイン", "app.containers.Therapist.scheduleLastUpdateTime": "最終シフト更新日時", "app.containers.Therapist.createdAt": "アカウント作成日", "app.containers.Therapist.explainNickname": "お客様に表示される名前です", "app.containers.Therapist.explainFullName": "お客様には表示されません", "app.containers.Therapist.explainPhone": "国コードを含めてください。 日本のコードは+81です。", "app.containers.Therapist.exportAll": "すべてのセラピストをエクスポート", "app.containers.Therapist.exportFiltered": "フィルタリングされたセラピストをエクスポート", "app.containers.Therapist.exportBlacklist": "セラピスト別ブロックリスト", "app.containers.Therapist.exportFavorite": "お気に入りランキング", "app.containers.Therapist.specialSetting": "特別設定", "app.containers.Therapist.messageConfirmTreatmentSetting": "オンにした場合、最低施術時間が90分以上の予約のみを受け付けます.", "app.containers.Therapist.displaySetting": "新着表示", "app.containers.Therapist.titleConfirmDisplaySetting": "確認", "app.containers.Therapist.messageConfirmDisplaySetting": "こちらのセラピストを”新着セラピスト”に表示しますか？", "app.containers.Therapist.introduction": "自己紹介", "app.containers.Therapist.noteIntroduction": "(一言メッセージ)", "app.containers.Therapist.infomation": "基本情報", "app.containers.Therapist.minutes": "分", "app.containers.Therapist.viewMore": "続きを読む", "app.containers.Therapist.midnightSupport": "深夜対応可能エリア", "app.containers.Therapist.departurePoint": "出発地", "app.containers.Therapist.confirmRate": "承認率", "app.containers.Therapist.responseRate": "返答率", "app.containers.Therapist.responseTime": "返答時間", "app.containers.Therapist.avgResponseTime": "平均{time}以内", "app.containers.Therapist.cancelCount": "確定予約キャンセル数", "app.containers.Therapist.lastLoginCard": "最終ログイン", "app.containers.Therapist.remove": "最終ログイン", "app.containers.Therapist.cooperation": "連携エージェント", "app.containers.Therapist.action": "ACTION", "app.containers.Therapist.linkingDes": "連携先", "app.containers.Therapist.linkedAgent": "連携するエージェント", "app.containers.Therapist.linkingTypes.agent": "エージェント", "app.containers.Therapist.edit": "編集", "app.containers.Therapist.statuses.LOCKED": "ブロック", "app.containers.Therapist.statuses.INACTIVE": "稼働休止中", "app.containers.TherapistDetail.WALKING": "徒歩", "app.containers.TherapistDetail.BICYCLE": "自転車", "app.containers.TherapistDetail.SCOOTER": "原付", "app.containers.TherapistDetail.BIKE": "バイク", "app.containers.TherapistDetail.TRAIN": "電車", "app.containers.TherapistDetail.TAXI": "タクシー", "app.containers.TherapistDetail.CAR": "自家用車", "app.containers.TherapistDetail.OTHER": "その他", "app.containers.TherapistDetail.messageChangeStatusSuccess": "セラピスト{id}の{action}に成功！", "app.containers.TherapistDetail.messageRemoveSuccess": "セラピスト {id} の削除に成功！", "app.containers.TherapistDetail.agent": "連携エージェント", "app.components.TherapistGroupAction.messageChangeStatusSuccess": "セラピスト{id}の{action}に成功！", "app.components.TherapistGroupAction.messageRemoveSuccess": "セラピスト {id} の削除に成功！", "app.containers.CreateTherapist.title": "新規セラピスト登録", "app.containers.BookingMenu.header": "施術メニュー", "app.containers.BookingMenu.Detail.id": "id", "app.containers.BookingMenu.Detail.menuImage": "画像", "app.containers.BookingMenu.Detail.detail": "細部", "app.containers.BookingMenu.Detail.type": "タイプ", "app.containers.BookingMenu.Detail.status": "ステータス", "app.containers.BookingMenu.Detail.published": "公開済み", "app.containers.BookingMenu.Detail.presetPrice": "事前設定", "app.containers.BookingMenu.Detail.fee": "料金", "app.containers.BookingMenu.Detail.isDefault": "既定", "app.containers.BookingMenu.Detail.price": "料金", "app.containers.BookingMenu.Detail.taxed": "課税", "app.containers.BookingMenu.Detail.minutes": " 分", "app.containers.BookingMenu.Detail.extras": "補足", "app.containers.BookingMenu.Detail.unPublish": "未公開", "app.containers.BookingMenu.Detail.unTaxed": "固定されていない", "app.components.BookingMenuForm.title": "メニュー名", "app.components.BookingMenuForm.detail": "メモ", "app.components.BookingMenuForm.options": "料金", "app.components.BookingMenuForm.isDefault": "既定", "app.components.BookingMenuForm.presetPrice": "事前設定", "app.components.BookingMenuForm.basePrice": "本体価格", "app.components.BookingMenuForm.taxed": "価格込み税", "app.components.BookingMenuForm.type": "タイプ", "app.components.BookingMenuForm.tags": "タグ", "app.components.BookingMenuForm.extras": "エキストラ", "app.components.BookingMenuForm.public": "パブリック", "app.components.BookingMenuForm.unPublic": "未公開", "app.components.BookingMenuForm.standard": "単品", "app.components.BookingMenuForm.combo": "パックメニュー", "app.components.BookingMenuForm.minutes": "分", "app.components.BookingMenuForm.updatedSuccess": "更新成功 ！", "app.components.BookingMenuForm.createdSuccess": "正常に作成されました。!", "app.components.BookingMenuForm.validation.title": "タイトルは1〜30文字です", "app.components.BookingMenuForm.validation.tags": "タグは1から30文字の間でなければなりません（英数字とハイフンのみ）", "app.components.BookingMenuForm.validation.price": "価格はゼロより良い数値でなければなりません", "app.components.BookingMenuForm.validation.duration": "期間はゼロより良い数値でなければなりません", "app.components.BookingMenuForm.validation.tax": "税率が0と100の間にあることを確認してください", "app.components.BookingMenuForm.validation.icon": "ファイルサイズ最大{size}MB", "app.components.BookingMenuForm.validation.image": "ファイルサイズ最大{size}MB", "app.components.BookingMenuForm.validation.extensionPrice": "延長価格を入力してください", "app.components.BookingMenuForm.validation.presetPrices": "値はゼロより大きい必要があります", "app.components.BookingMenuForm.taxRate": "税率", "app.components.BookingMenuForm.extension": "拡張", "app.components.BookingMenuForm.menuImage": "画像", "app.components.BookingMenuForm.menuIcon": "アイコン", "app.components.BookingMenuForm.menuIconDescription": "最大 {value}MB", "app.components.BookingMenuForm.menuImageDescription": "最大 {value}MB", "app.containers.Booking.selectTherapist": "セラピスト", "app.containers.Booking.confirm": "セラピストへ通知を送る", "app.containers.Booking.exportCancellationReason": "キャンセル理由をエクスポートする", "app.containers.Booking.assignForm.assignSuccess": "セラピストへの予約を無事に割り当てましょう！", "app.containers.Booking.assignForm.selectTherapist": "セラピストを選択してください", "app.containers.Booking.assignForm.selectTherapistConfirm": "このセラピストに予約を割り当てますか？", "app.containers.Booking.assignForm.confirm": "確認する", "app.containers.Booking.assignForm.emptyTherapist": "割り当てるセラピストを選んでください", "app.containers.CancelBooking.isCharged": "キャンセル料は発生しますか？", "app.containers.CancelBooking.paymenthMethod": "顧客はどのように支払いますか？", "app.containers.CancelBooking.other": "お客様はすでに支払い済みです/ 料金はシステム外で請求されます。", "app.containers.CancelBooking.creditcard": "今すぐ顧客のクレジットカードに請求します。", "app.containers.CancelBooking.note": "（注：これはシステムによって自動的に処理されます）。", "app.containers.CancelBooking.notCharge": "いいえ", "app.containers.CancelBooking.charge": "はい（キャンセル料：施術代全額）", "app.containers.CancelBooking.penalty": "セラピストポイント", "app.containers.CancelBooking.cancelCount": " 確定予約キャンセル数", "app.containers.CancelBooking.titleConfirmModal": "確認", "app.containers.CancelBooking.messageConfirmModal": "この予約リクエストをキャンセルしますか？（予約ID：{bookingId}）", "app.components.Autocomplete.title": "タイトル", "app.components.Confirm.okButton": "はい", "app.components.Confirm.closeButton": "キャンセル", "app.containers.CancelBooking.reason": "理由", "app.containers.CancelBooking.header": "予約キャンセル", "app.containers.EditPenaltyBooking.header": "ペナルティー（編集）", "app.containers.EditPenaltyBooking.penalty": "セラピストポイント", "app.containers.EditPenaltyBooking.cancelCount": "確定予約キャンセル数", "app.containers.Schedule.header": "スケジュール", "app.containers.Schedule.hour": "HH[時]", "app.containers.Schedule.hourMinute": "HH[時]mm[分]", "app.containers.Schedule.hourMinute24h": "kk[時]mm[分]", "app.containers.Schedule.therapist": "セラピスト", "app.containers.Schedule.notWorking": "働いていない", "app.containers.Schedule.noInfo": "情報なし", "app.containers.Schedule.currentWeek": "今週", "app.containers.Schedule.nextWeek": "来週", "app.containers.Schedule.workingTime": "予定を外す", "app.containers.Schedule.schedule": "労働時間", "app.containers.Schedule.outWorkingTime": "勤務時間外", "app.containers.Schedule.bookingTime": "予約する", "app.containers.Sales.header": "セールスマネジメント", "app.containers.Sales.totalCustomer": "利用顧客", "app.containers.Sales.totalTherapist": "トータルセラピスト", "app.containers.Sales.totalCompletedBooking": "施術完了", "app.containers.Sales.totalCanceledBooking": "取消合計", "app.containers.Sales.todayRevenue": "決済金額", "app.containers.Sales.totalRevenue": "決済金額", "app.containers.Sales.totalProfit": "予約手数料", "app.containers.Header.MyPage.changePassword": "パスワードを変更する", "app.containers.Header.MyPage.editProfile": "プロファイル編集", "app.containers.Header.MyPage.myProfile": "私のプロフィール", "app.containers.Header.MyPage.logout": "ログアウト", "app.containers.ChangePassword.title": "パスワードを変更する", "app.containers.ChangePassword.oldPassword": "以前のパスワード", "app.containers.ChangePassword.newPassword": "新しいパスワード", "app.containers.ChangePassword.confirmNewPassword": "パスワードを認証する", "app.containers.ChangeInformationAccount.title": "プロファイル編集", "app.containers.ChangeInformationAccount.fullName": "フルネーム", "app.containers.ChangeInformationAccount.email": "Eメール", "app.containers.ChangeInformationAccount.phoneNumber": "電話番号", "app.containers.ChangeInformationAccount.timezone": "タイムゾーン", "app.components.ConfigurationForm.validation.data": "所要時間は5分から48時間の範囲で必要です", "app.components.ConfigurationForm.description": "説明", "app.components.ConfigurationForm.descriptionPlaceholder": "この構成は何に使用するかを説明する必要があります。", "app.components.ConfigurationForm.duration": "期間", "app.components.ConfigurationForm.updatedSuccess": "更新成功！", "app.containers.Configuration.header": "構成", "app.containers.Payout.header": "支払い管理", "app.containers.Payout.payoutId": "支払いID", "app.containers.Payout.therapist": "セラピスト名", "app.containers.Payout.period": "計算期間", "app.containers.Payout.branch": "FC", "app.containers.Payout.amount": "決済金額", "app.containers.Payout.status": "ステータス", "app.containers.Payout.notePayout": "終了した期間のみがここに表示されます", "app.containers.Payout.messageMarkAsPaidSuccess": "支払い完了チェックをしています", "app.containers.Payout.export": "支払いリストをエクスポートする", "app.containers.Payout.exportBankTransfer": "総合振込データ", "app.containers.Payout.markAllAsPaid": "全てを支払い完了にする", "app.components.PayoutCell.payout": "展望", "app.components.PayoutCell.branch": "展望", "app.components.TherapistPayoutEarning.payout": "計算期間", "app.components.TherapistPayoutEarning.branch": "FC", "app.components.TherapistPayoutEarning.startingBalance": "前回繰越分", "app.components.TherapistPayoutEarning.bookingValue": "今期間の決済金額合計", "app.components.TherapistPayoutEarning.estimatedEarning": "推定収益", "app.components.TherapistPayoutEarning.grossEarning": "総収益", "app.components.TherapistPayoutEarning.cashPayment": "現金での受け取り金額", "app.components.TherapistPayoutEarning.commissionNew": "新人期間 予約手数料 (10%)", "app.components.TherapistPayoutEarning.hoguguCommission": "予約手数料", "app.components.TherapistPayoutEarning.commissionDetailed": "詳細", "app.components.TherapistPayoutEarning.cardFee": "決済手数料", "app.components.TherapistPayoutEarning.bonus": "新人ボーナス", "app.components.TherapistPayoutEarning.payoutMidCycle": "ペイアウトミッドサイクル", "app.components.TherapistPayoutEarning.bankFee": "銀行の手数料", "app.components.TherapistPayoutEarning.couponValue": "﻿クーポン利用額", "app.components.TherapistPayoutEarning.currentBalance": "セラピストへのお支払い金額", "app.components.TherapistPayoutEarning.makePayout": "いをする", "app.components.TherapistPayoutEarning.view": "見る", "app.components.TherapistPayout.earnings": "稼ぎ", "app.containers.TherapistPayoutModal.bookings": "予約", "app.containers.TherapistPayoutModal.payouts": "お支払いリスト", "app.containers.TherapistPayoutModal.earnings": "アクティブな期間", "app.containers.TherapistPayoutModal.reviews": "レビュー", "app.containers.TherapistPayoutModal.blacklist": "ブロックしたお客様", "app.containers.TherapistPayoutModal.bank": "銀行口座情報", "app.components.TherapistPayout.exportPayout": "支払明細をエクスポート", "app.components.TherapistPayout.payoutId": "支払いID", "app.components.TherapistPayout.period": "期間", "app.components.TherapistPayout.branch": "ブランチ", "app.components.TherapistPayout.amount": "決済金額", "app.components.TherapistPayout.payoutStatus": "支払いステータス", "app.components.TherapistPayout.actions": "行動", "app.components.PayoutIncomes.bookingId": "予約ID", "app.components.PayoutIncomes.chargedTime": "予約完了日", "app.components.PayoutIncomes.bookingDate": "予約日", "app.components.PayoutIncomes.amount": "決済金額", "app.components.PayoutIncomes.commission": "手数料", "app.components.PayoutIncomes.bookingStatus": "ステータス", "app.components.PayoutIncomes.payoutList": "お支払いリスト", "app.components.PayoutIncomes.bookingInPayout": "1. 今回のお支払いに含まれる予約", "app.components.PayoutIncomes.requestPayment": "2. お支払い内容詳細", "app.components.PayoutIncomes.statementPreview": "3. 即日払い管理", "app.components.StatementPreview.payoutStatement": "セラピストの支払い明細", "app.components.StatementPreview.statementDate": "期間の開始日", "app.components.StatementPreview.statementPeriod": "計算期間", "app.components.StatementPreview.amount": "金額", "app.components.StatementPreview.branch": "FC", "app.components.StatementPreview.items": "項目", "app.components.StatementPreview.startingBalance": "前回繰越分", "app.components.StatementPreview.grossBooking": "今期間の決済金額合計", "app.components.StatementPreview.bookingValue": "施術代金", "app.components.StatementPreview.midnightFee": "深夜料金", "app.components.StatementPreview.cash": "現金での受け取り金額", "app.components.StatementPreview.commissionFee": "予約手数料", "app.components.StatementPreview.commissionZero": "{year}年{month}月までの 新人手数料 (0%)", "app.components.StatementPreview.commissionNew": "新人期間 予約手数料 (10%)", "app.components.StatementPreview.commission": "予約手数料 (25%)", "app.components.StatementPreview.consumptionTax": "消費税 ({percent}%)", "app.components.StatementPreview.consumptionTaxNote": "印は消費税{percent}％対象", "app.components.StatementPreview.cardFee": "決済手数料", "app.components.StatementPreview.bonus": "ボーナス", "app.components.StatementPreview.couponValue": "﻿クーポン利用額", "app.components.StatementPreview.closingBalance": "今期間の通常支払合計額 ", "app.components.StatementPreview.totalAmount": "振込額 ", "app.components.StatementPreview.bankFee": "振込手数料", "app.components.StatementPreview.totalRequestPayment": "今期間支払い済[即日払い]合計額", "app.components.StatementPreview.hoguguFee": "今期間支払い済[即日払い]の手数料合計 (10%)", "app.components.StatementPreview.bankFeeRequest": "今期間支払い済[即日払い]振込手数料合計 ({fee}円×回数)", "app.components.StatementPreview.crisisFeeRequest": "今期間支払い済[即日払い]危機管理手数料合計 ({fee}円 X 回数)", "app.components.StatementPreview.crisisManagementFee": "危機管理手数料", "app.components.TherapistEarning.backToPayout": "クリックして支払い履歴を表示します", "app.components.ReviewModal.title": "レビュー", "app.components.ReviewItem.booking": "今回の施術について", "app.components.ReviewItem.place": "訪問先について", "app.components.ReviewItem.notReview": "まだレビューしていません", "app.components.ReviewItem.confirmTitle": "確認", "app.components.ReviewItem.confirmContent": "レビューを削除してもよろしいですか？", "app.components.ReviewItem.cancel": "いいえ", "app.components.ReviewItem.ok": "はい", "app.components.TherapistReviewTab.averageRating": "星評価", "app.components.TherapistReviewTab.reviewList": "レビュー内容", "app.components.TherapistReviewTab.stars": "星", "app.components.TherapistReviewTab.descriptionAverageRating": "{sumReviewer}件のレビュー", "app.components.TherapistReviewTab.noReview": "レビューなし", "app.components.TherapistReviewTab.loadMore": "もっと読み込む", "app.containers.Customer.header": "お客様管理", "app.containers.Customer.id": "#ID", "app.containers.Customer.name": "名前", "app.containers.Customer.phone": "電話", "app.containers.Customer.email": "メール", "app.containers.Customer.point": "ポイント", "app.containers.Customer.createdDate": "作成日", "app.containers.Customer.lastBookingDate": "最終予約日", "app.containers.Customer.clearFilter": "フィルターをクリア", "app.containers.Customer.exportAllCustomers": "すべての顧客をエクスポートする", "app.containers.Customer.exportFilteredCustomers": "フィルターされた顧客をエクスポートする", "app.containers.Customer.exportBlacklistCustomer": "ブロックランキング", "app.containers.Customer.identityCard": "身分証", "app.containers.Customer.viewImage": "画像を確認", "app.containers.Customer.notChecked": "確認待ち", "app.containers.Customer.setPointSuccessful": "保存されました", "app.containers.Customer.ok": "確認OK", "app.components.CustomerReviewModal.reviews": "レビュー", "app.components.ReviewTab.reviewRating": "レビュー評価", "app.components.ReviewTab.averageRating": "星評価", "app.components.ReviewTab.reviewList": "レビュー内容", "app.components.ReviewTab.stars": "星", "app.components.ReviewTab.descriptionAverageRating": "{sumReviewer}件のレビュー", "app.components.ReviewTab.noReview": "レビューなし", "app.components.ReviewTab.loadMore": "もっと読み込む", "app.components.ThrapistBlacklist.id": "#ID", "app.components.ThrapistBlacklist.name": "名前", "app.components.ThrapistBlacklist.phone": "電話", "app.components.ThrapistBlacklist.email": "メール", "app.components.TherapistBank.bank": "銀行口座情報", "app.components.TherapistBank.bankName": "金融機関名", "app.components.TherapistBank.branchName": "支店名", "app.components.TherapistBank.branchCode": "支店コード", "app.components.TherapistBank.cardType": "口座種別", "app.components.TherapistBank.cardNumber": "口座番号", "app.components.TherapistBank.name": "口座名義", "app.components.ThrapistReport.revenue": "今月の売上", "app.components.ThrapistReport.revenueLastMonth": "先月の売上", "app.components.ThrapistReport.totalRequest": "今月のリクエスト件数 | 指名予約", "app.components.ThrapistReport.totalRequestLastMonth": "先月のリクエスト件数", "app.components.ThrapistReport.times": "件", "app.components.ThrapistReport.totalCancel": "全キャンセル数", "app.components.ThrapistReport.totalCancelLastMonth": "先月のキャンセル実績", "app.components.ThrapistReport.couponQuantum": "今月のクーポン利用件数", "app.components.ThrapistReport.couponQuantumLastMonth": "先月のクーポン利用件数", "app.components.ThrapistReport.couponValue": "今月のクーポン利用額", "app.components.ThrapistReport.couponValueLastMonth": "先月のクーポン利用額", "app.components.ThrapistReport.cancelTimes": "キャンセル", "app.components.ThrapistReport.customerCancel": "お客様", "app.components.ThrapistReport.therapistCancel": "セラピスト", "app.components.ThrapistReport.adminCancel": "運営", "app.containers.Announcement.header": "アナウンス管理", "app.containers.Announcement.headerCreate": "アナウンスを作成する", "app.containers.Announcement.headerEdit": "アナウンスを編集", "app.containers.Announcement.create": "新しいアナウンスを作成する", "app.containers.Announcement.banner": "バナー", "app.containers.Announcement.id": "#ID", "app.containers.Announcement.announcement": "アナウンス", "app.containers.Announcement.status": "地位", "app.containers.Announcement.action": "作用", "app.containers.Announcement.announcementActivate": "有効", "app.containers.Announcement.announcementDeactivate": "無効", "app.containers.Announcement.activateAnnouncement": "Activate", "app.containers.Announcement.deactivateAnnouncement": "Deactivate", "app.containers.Announcement.Form.link": "リンク", "app.containers.Announcement.Form.banner": "バナー", "app.containers.Announcement.Form.announcement": "アナウンス", "app.containers.Announcement.Form.linkNote": "顧客がバナーまたはアナウンスポップアップをタップしたときのエンドポイント", "app.containers.Announcement.Form.bannerNote": "画像の幅は981ピクセル、高さは240ピクセルである必要があります", "app.containers.Announcement.Form.announcementNote": "画像の幅は981ピクセル、高さは1308ピクセルである必要があります", "app.containers.Announcement.Form.validation.needImage": "2つのフィールド（バナーまたはアナウンス）のうち少なくとも1つが必要です", "app.containers.Coupon.header": "クーポン管理", "app.containers.Coupon.headerCreate": "クーポンを作成する", "app.containers.Coupon.headerEdit": "クーポンの編集", "app.containers.Coupon.create": "新しく作る", "app.containers.Coupon.id": "#ID", "app.containers.Coupon.couponCode": "コード", "app.containers.Coupon.value": "金額", "app.containers.Coupon.duration": "利用期限", "app.containers.Coupon.title": "題名", "app.containers.Coupon.description": "説明", "app.containers.Coupon.status": "ステータス", "app.containers.Coupon.action": "操作", "app.containers.Coupon.couponActive": "有効", "app.containers.Coupon.couponDeactive": "無効", "app.containers.Coupon.couponActivate": "Activate", "app.containers.Coupon.couponDeactivate": "Deactivate", "app.containers.Coupon.couponNotFound": "クーポンが見つかりません", "app.containers.Coupon.couponRemoveSucces": "クーポンを正常に削除します", "app.containers.Coupon.couponRemoveFailed": "クーポンを削除できません", "app.containers.Coupon.removeConfirm": "クーポンを削除してもよろしいですか？", "app.containers.Coupon.editConfirm": "この情報を変更してもよろしいですか?", "app.containers.Coupon.searchKeyword": "コード、題名、説明で検索", "app.containers.Coupon.btnSearch": "適用する", "app.containers.Coupon.Form.couponList": "クーポン一覧", "app.containers.Coupon.Form.headerInfo": "クーポン情報", "app.containers.Coupon.Form.id": "ID", "app.containers.Coupon.Form.code": "コード", "app.containers.Coupon.Form.discountType": "値引きタイプ", "app.containers.Coupon.Form.discountAmount": "割引金額", "app.containers.Coupon.Form.fixAmount": "金額", "app.containers.Coupon.Form.rate": "割合", "app.containers.Coupon.Form.amount": "金額", "app.containers.Coupon.Form.title": "題名", "app.containers.Coupon.Form.description": "説明", "app.containers.Coupon.Form.headerRule": "クーポンのルールと条件", "app.containers.Coupon.Form.start": "開始日", "app.containers.Coupon.Form.end": "終了日", "app.containers.Coupon.Form.quantum": "制限（このコードを使用できる回数）", "app.containers.Coupon.Form.quantumNote": "0は使用時間の制限がないことを意味します", "app.containers.Coupon.Form.min": "クーポンを適用するための最小簿価", "app.containers.Coupon.Form.minNote": "0は、最小予約額を制限しないことを意味します", "app.containers.Coupon.Form.limitPerUser": "ユーザーあたりの制限（ユーザーあたりこのコードを使用できる回数）", "app.containers.Coupon.Form.limitPerUserNote": "0は、ユーザーごとに制限がないことを意味します", "app.containers.Coupon.Form.neverExpired": "期限切れになることはありません", "app.containers.Coupon.Form.firstTimeLimit": "初回限定クーポン", "app.containers.Coupon.Form.firstTimeLimitNote": "Hoguguを使用したことがないお客様にのみお申し込みください", "app.containers.Coupon.Form.availableTime": "クーポンご利用可能な時間", "app.containers.Coupon.Form.availableBookingStartTimeFrom": "開始時間", "app.containers.Coupon.Form.availableBookingStartTimeTo": "終了時間", "app.containers.Coupon.Form.headerAnnouncement": "バナーとアナウンス（オプション）", "app.containers.Coupon.Form.note": "バナーとアナウンスを選択して、クーポンにリンクします。 顧客がアプリでクーポンのバナーとアナウンスを見ることができるように", "app.containers.Coupon.Form.link": "リンク", "app.containers.Coupon.Form.linkNote": "顧客がバナーまたはアナウンスポップアップをタップしたときのエンドポイント", "app.containers.Coupon.Form.banner": "バナー", "app.containers.Coupon.Form.bannerNote": "画像の幅は981ピクセル、高さは240ピクセルである必要があります", "app.containers.Coupon.Form.announcement": "発表", "app.containers.Coupon.Form.announcementNote": "画像の幅は981ピクセル、高さは1308ピクセルである必要があります", "app.containers.Coupon.Form.couponEditUnload": "別のページに移動すると、編集した情報は失われます。", "app.containers.Coupon.Form.couponCreateUnload": "別のページに移動すると、入力した情報は失われます。", "app.containers.Coupon.Form.validation.codeLength": "コードは3〜5文字である必要があります", "app.containers.Coupon.Form.validation.titleLength": "タイトルは25文字以下である必要があります", "app.containers.Coupon.Form.validation.needImage": "2つのフィールド（バナーまたはアナウンス）のうち少なくとも1つが必要です", "app.containers.Coupon.Form.validation.amountLimit": "値は1〜1,000,000でなければなりません", "app.containers.Coupon.Form.validation.amountLimitRate": "クーポン金額は1から100までを入力してください。", "app.containers.Coupon.Form.showCoupon": "マイクーポンに表示", "app.containers.Coupon.Form.showCouponInList": "クーポンをお客様のクーポンー覧に表示します。", "app.containers.Coupon.Form.show": "表示する", "app.containers.Coupon.Form.hide": "表示しない", "app.containers.Coupon.Form.minimumAmount": "クーポンを利用可能な最低金額", "app.containers.Coupon.Form.noteMinimumAmount": "クーポンを適用できる最低利用料金を設定できます。", "app.containers.Coupon.Form.maximumNumber": "利用枚数の上限", "app.containers.Coupon.Form.noteMaximumNumber": "クーポンの利用枚数に上限を設定できます", "app.containers.Coupon.Form.maximumNumberUses": "カスタマーごとの利用回数の上限", "app.containers.Coupon.Form.noteMaximumNumberUses": "同じお客様がクーポンを利用できる回数に上限を設定できます", "app.containers.Coupon.Form.startDate": "利用開始日時", "app.containers.Coupon.Form.noteStartDate": "指定した日時以降の予約リクエストでクーポンを利用できます。", "app.containers.Coupon.Form.endDate": "利用終了日時", "app.containers.Coupon.Form.noteEndDate": "指定した日時までの予約リクエストでクーポンを利用できます。", "app.containers.Coupon.Form.neverExpires": "期限切れになることはありません", "app.components.CancellationReason.header": "キャンセル", "app.components.CancellationReason.therapistCancelHeader": "キャンセル（確定予約）", "app.components.CancellationReason.adminCancelHeader": "運営キャンセル", "app.components.CancellationReason.autoCancelHeader": "自動キャンセル", "app.components.CancellationReason.cancellationNoteTitle": "キャンセル理由", "app.components.CancellationReason.therapistDenyNoteTitle": "キャンセル（予約リクエスト）", "app.components.CancellationReason.therapistCancelSubtitle": "セラピスト自身の都合", "app.components.CancellationReason.customerCancelSubtitle": "カスタマーの都合または規約違反", "app.components.CancellationReason.cancellationReasonTitle": "アンケート", "app.components.CancellationReason.cancellationCharge": "キャンセル料", "app.components.CancellationReason.messageChargeReason": "キャンセル料の発生する予約です", "app.containers.Feedback.header": "サービス評価", "app.containers.Feedback.userId": "ユーザーID", "app.containers.Feedback.name": "ユーザー名", "app.containers.Feedback.role": "ユーザー種別", "app.containers.Feedback.datetime": "終了日時", "app.containers.Feedback.comment": "コメント", "app.containers.Feedback.bookingId": "直前の施術情報（#ID）", "app.components.SetCustomerPointForm.title": "ポイント付与/減点", "app.components.SetCustomerPointForm.subTitle": "ポイント", "app.components.SetCustomerPointForm.availablePoints": "保有ポイント:", "app.components.SetCustomerPointForm.increasePoint": "付与する", "app.components.SetCustomerPointForm.reducePoint": "減点する", "app.components.SetCustomerPointForm.increasePointValidation": "ポイントは 1 ～ 9,999 の範囲でなければなりません。", "app.components.SetCustomerPointForm.reducePointValidation": "ポイントは 1 ～ 9,999,999 の範囲でなければなりません。", "app.components.SetCustomerPointForm.confirmRewardPointMessage": "{customerName}に{point}ポイント付与しますか？\n付与後はポイントの削除はできません。ご確認ください", "app.components.SetCustomerPointForm.confirmDeductPointMessage": "{customerName}に{point}ポイントを減点しますか？減点後はポイントがなくなるので、ご確認ください", "app.components.BookingDetailModal.customerName": "お客様名", "app.components.BookingDetailModal.phoneNumber": "電話番号", "app.components.BookingDetailModal.gender": "お客様の性別", "app.components.BookingDetailModal.any": "どちらでも", "app.components.BookingDetailModal.man": "男性", "app.components.BookingDetailModal.woman": "女性", "app.components.BookingDetailModal.dateBooking": "日時", "app.components.BookingDetailModal.duration": "施術時間", "app.components.BookingDetailModal.minutes": "分", "app.components.BookingDetailModal.prefecture": "都道府県", "app.components.BookingDetailModal.therapistName": "セラピスト", "app.components.BookingDetailModal.therapistPhoneNumber": "セラピストの電話番号", "app.components.BookingDetailModal.address": "住所", "app.components.BookingDetailModal.bookingId": "施術詳細", "app.containers.BookingReview.header": "レビュー管理", "app.containers.BookingReview.applyFilter": "適用する", "app.containers.BookingReview.exportReview": "レビューをエクスポート", "app.containers.BookingReview.bookingId": "#Booking ID", "app.containers.BookingReview.createdDate": "日時", "app.containers.BookingReview.typeAccount": "ユーザー種別", "app.containers.BookingReview.name": "ユーザー名", "app.containers.BookingReview.id": "#ID", "app.containers.BookingReview.review": "コメント", "app.containers.BookingReview.booking": "今回の施術について", "app.containers.BookingReview.place": "訪問先について", "app.containers.BookingReview.notReview": "まだレビューしていません", "app.components.Comment.comment": "メモ ・共有事項", "app.components.Comment.allComment": "メモ ・共有一覧", "app.components.Comment.atatchBtn": "添付イメージ", "app.components.Comment.note": "イメージ画像,mp4,PDFファイルが添付可能。容量は1ファイル最大50メガバイト、ファイル数は最大5ファイルまでとなります。", "app.components.Comment.submit": "保存", "app.components.Comment.titleConfirmModal": "確認", "app.components.Comment.messagePostCommentFailed": "何かが間違っていました。もう一度やり直してください", "app.components.Comment.messageConfirmDelete": "削除してもよろしいですか？", "app.components.Comment.emptyComment": "メモ・共有したいことなどを残すことができます", "app.components.Comment.too-many-files": "ファイルが多すぎます", "app.components.Comment.file-too-large": "ファイルが{fileSize}より大きい", "app.components.Comment.errorEmptyContent": "メモを残していませんので、メモや共有したいものなどを残してください。", "app.containers.BookingEdit.edit": "編集", "app.containers.BookingEdit.header": "メニューの編集", "app.containers.BookingEdit.confirm": "確認", "app.containers.BookingEdit.contentConfirm": "変更内容を保存してもよろしいですか?", "app.containers.BookingEdit.contentConfirmCoupon": "変更後の価格では条件が満たないため、ご指定のクーポンをご利用いただけません。\nクーポンのご利用をキャンセルしてもよろしいですか？", "app.containers.BookingEdit.closeButton": "NO", "app.containers.BookingEdit.okButton": "YES", "app.containers.BookingEditForm.mainMenu": "基本メニュー", "app.containers.BookingEditForm.extension": "延長メニュー", "app.containers.BookingEditForm.addExtension": "メニューの追加", "app.containers.BookingEditForm.cancel": "キャンセル", "app.containers.BookingEditForm.update": "保存する", "app.containers.BookingEditForm.total": "合計", "app.containers.BookingEditForm.time": "時間", "app.containers.BookingEditForm.minutes": "分", "app.containers.BookingEditForm.requireMenu": "以下のメニューからお選びください", "app.containers.BookingEditForm.minimumDuration": "最短期間は60分です", "app.components.MenuEditBooking.menu": "メニュー", "app.components.MenuEditBooking.duration": "施術時間", "app.components.MenuEditBooking.price": "料金", "app.containers.TherapistDetailPage.manageTherapist": "プセラピスト管理", "app.containers.TherapistDetailPage.profile": "プロフィール", "app.containers.TherapistDetailPage.bookings": "予約", "app.containers.TherapistDetailPage.payouts": "お支払いリスト", "app.containers.TherapistDetailPage.earnings": "アクティブな期間", "app.containers.TherapistDetailPage.reviews": "レビュー", "app.containers.TherapistDetailPage.blacklist": "ブロックしたお客様", "app.containers.TherapistDetailPage.bank": "銀行口座情報", "app.containers.TherapistDetailPage.sale": "実績", "app.containers.TherapistDetailPage.coupon": "クーポン", "app.containers.BookingChatting.header": "チャット履歴", "app.containers.BookingChatting.empty": "左上の検索窓に、確認したい予約IDを入力してください", "app.containers.BookingChatting.emptyChat": "このIDでチャットの利用はありません", "app.containers.BookingChatting.bookingId": "予約ID", "app.containers.BookingChatting.timestamp": "YYYY年MM月DD日 HH:mm", "app.containers.BookingChatting.errorTitle": "予約IDが間違っています", "app.containers.BookingChatting.errorMessage": "もう一度予約IDを確認してください", "app.components.MemoRequest.close": "キャンセル", "app.components.MemoRequest.save": "保存", "app.containers.PaymentRequest.header": "即日払い管理", "app.containers.PaymentRequest.apply": "適用する", "app.containers.PaymentRequest.paymentId": "即日払い申請ID", "app.containers.PaymentRequest.therapistId": "セラピスト ID", "app.containers.PaymentRequest.therapistName": "セラピスト名", "app.containers.PaymentRequest.memo": "メモ", "app.containers.PaymentRequest.addMemo": "+ メモを追加", "app.containers.PaymentRequest.createdAt": "申請日時", "app.containers.PaymentRequest.reviewDate": "判定日時", "app.containers.PaymentRequest.amount": "申請金額", "app.containers.PaymentRequest.status": "ステータス", "app.containers.PaymentRequest.action": "操作", "app.containers.PaymentRequest.approval": "承認", "app.containers.PaymentRequest.confirmTitle": "確認", "app.containers.PaymentRequest.confirmContent": "申請を承認してもよろしいですか?", "app.containers.PaymentRequest.confirmPaidContent": "この申請を「支払い完了」に変更しますか？", "app.containers.PaymentRequest.confirmRejectContent": "却下してもよろしいですか?", "app.containers.PaymentRequest.reject": "却下", "app.containers.PaymentRequest.close": "いいえ", "app.containers.PaymentRequest.ok": "はい", "app.containers.PaymentRequest.closeMemo": "キャンセル", "app.containers.PaymentRequest.saveMemo": "保存", "app.containers.PaymentRequest.titleAddMemo": "メモ ・共有事項", "app.containers.PaymentRequest.titleEditMemo": "メモ・共有事項の編集", "app.containers.PaymentRequest.placeholderMemo": "共有事項を入力してください", "app.containers.PaymentRequest.titleReject": "却下の理由", "app.containers.PaymentRequest.placeholderReject": "セラピストに却下の理由を送信したい場合は、入力してください（任意)", "app.containers.PaymentRequest.contentRejectMulti": "選択した{count}件にコメント", "app.containers.PaymentRequest.closeReject": "キャンセル", "app.containers.PaymentRequest.acceptReject": "保存", "app.containers.PaymentRequest.markAsPaid": "支払い完了チェック", "app.containers.PaymentRequest.all": "すべて", "app.containers.PaymentRequest.rejected": "却下", "app.containers.PaymentRequest.approved": "承認", "app.containers.PaymentRequest.paid": "支払い済", "app.containers.PaymentRequest.canceled": "キャンセル", "app.containers.PaymentRequest.expired": "期限切れ", "app.containers.PaymentRequest.request": "申請中", "app.containers.PaymentRequest.exportRequest": "即日払いリスト", "app.containers.PaymentRequest.exportListTransfer": "ステータス承認CSVエクスポート", "app.containers.RequestPaymentDetail.requestPayment": "即日払い管理", "app.containers.RequestPaymentDetail.header": "詳細", "app.containers.RequestPaymentDetail.therapistId": "セラピスト ID", "app.containers.RequestPaymentDetail.therapistName": "セラピスト名", "app.containers.RequestPaymentDetail.memo": "メモ", "app.containers.RequestPaymentDetail.addMemo": "+ メモを追加", "app.containers.RequestPaymentDetail.createdAt": "申請日時", "app.containers.RequestPaymentDetail.requestDate": "申請日時", "app.containers.RequestPaymentDetail.rejectedDate": "判定日時", "app.containers.RequestPaymentDetail.approvedDate": "判定日時", "app.containers.RequestPaymentDetail.paidDate": "判定日時", "app.containers.RequestPaymentDetail.canceledDate": "キャンセル日時", "app.containers.RequestPaymentDetail.expiredDate": "キャンセル日時", "app.containers.RequestPaymentDetail.amountRequest": "申請金額", "app.containers.RequestPaymentDetail.totalAmount": "トータル施術代金", "app.containers.RequestPaymentDetail.hoguguFee": "予約手数料", "app.containers.RequestPaymentDetail.crisisFee": "危機管理手数料", "app.containers.RequestPaymentDetail.consumptionTax": "消費税 ({percent}％)", "app.containers.RequestPaymentDetail.consumptionTaxNote": "(※印は消費税{percent}％対象)", "app.containers.RequestPaymentDetail.bankFee": "振込手数料", "app.containers.RequestPaymentDetail.amountReceive": "振込予定額", "app.containers.RequestPaymentDetail.approval": "承認", "app.containers.RequestPaymentDetail.reject": "却下", "app.containers.RequestPaymentDetail.reason": "理由", "app.components.ReviewForm.header": "編集", "app.components.ReviewForm.bookingId": "予約ID", "app.components.ReviewForm.reviewBooking": "今回の施術について", "app.components.ReviewForm.reviewPlace": "訪問先について", "app.components.ReviewForm.cancel": "キャンセル", "app.components.ReviewForm.save": "保存する", "app.components.ReviewForm.validate.booking": "今回の施術について 必須", "app.components.ReviewForm.validate.place": "訪問先について 必須", "app.components.ReviewForm.validate.minimum": "3文字以上で入力してください", "app.components.BookingNoteForm.header.customerNote": "施術内容について(編集)", "app.components.BookingNoteForm.header.parkingNote": "訪問先について(編集)", "app.components.BookingNoteForm.cancel": "キャンセル", "app.components.BookingNoteForm.save": "保存する", "app.components.ErrorModal": "OK", "app.components.AgentPayoutTable.payoutId": "支払いID", "app.components.AgentPayoutTable.agent": "エージェント名", "app.components.AgentPayoutTable.period": "計算期間", "app.components.AgentPayoutTable.amount": "決済金額", "app.components.AgentPayoutTable.status": "ステータス", "app.components.AgentPayoutTable.notePayout": "終了した期間のみがここに表示されます", "app.components.AgentPayoutTable.messageMarkAsPaidSuccess": "支払い完了チェックをしています", "app.components.AgentPayouts.exportPayout": "支払明細をエクスポート", "app.components.AgentStatementPreview.payoutStatement": "セラピストの支払い明細", "app.components.AgentStatementPreview.statementDate": "期間の開始日", "app.components.AgentStatementPreview.statementPeriod": "計算期間", "app.components.AgentStatementPreview.amount": "金額", "app.components.AgentStatementPreview.items": "項目", "app.components.AgentStatementPreview.startingBalance": "繰越したエージェント手数料", "app.components.AgentStatementPreview.grossBooking": "今期間の決済金額合計", "app.components.AgentStatementPreview.bookingValue": "施術代金", "app.components.AgentStatementPreview.midnightFee": "深夜料金", "app.components.AgentStatementPreview.totalAmount": "振込額 ", "app.components.AgentStatementPreview.bankFee": "振込手数料", "app.components.AgentStatementPreview.agentFee": "エージェント手数料", "app.components.AgentStatementPreview.consumptionTax": "消費税", "app.components.AgentStatementPreview.closingBalance": "今期間の通常支払合計額", "app.components.AgentStatementPreview.crisisFee": "危機管理手数料", "app.components.AgentStatementPreview.tax": "消費税 ({percent}%)", "app.components.AgentStatementPreview.taxNote": "(※印は消費税{percent}％対象)", "app.components.AgentPayoutIncomes.bookingId": "予約ID", "app.components.AgentPayoutIncomes.chargedTime": "予約完了日", "app.components.AgentPayoutIncomes.bookingDate": "予約日", "app.components.AgentPayoutIncomes.amount": "決済金額", "app.components.AgentPayoutIncomes.therapistId": "セラピスト ID", "app.components.AgentPayoutIncomes.therapistName": "セラピスト名", "app.components.AgentPayoutIncomes.bookingStatus": "ステータス", "app.components.AgentPayoutIncomes.payoutList": "お支払いリスト", "app.components.AgentPayoutIncomes.bookingInPayout": "1.今回のお支払いに含まれる予約", "app.components.AgentPayoutIncomes.statementPreview": "2. お支払い内容詳細", "app.components.AgentEarning.backToPayout": "クリックして支払い履歴を表示します", "app.components.AgentPayoutEarning.payout": "計算期間", "app.components.AgentPayoutEarning.startingBalance": "繰越したエージェント手数料", "app.components.AgentPayoutEarning.bookingValue": "今期間の決済金額合計", "app.components.AgentPayoutEarning.agentFee": "エージェント手数料", "app.components.AgentPayoutEarning.currentBalance": "セラピストへのお支払い金額", "app.components.MarkAllAsPaidModal.title": "全てを支払い完了にする", "app.components.MarkAllAsPaidModal.message": "{sum}名のセラピストに対して{start}から{end}までの期間を支払い完了しますか？", "app.components.updateCustomerForm.title": "基本情報 - 編集", "app.containers.BtnMoreBookingAction.CONFIRMED": "確定予約", "app.containers.BtnMoreBookingAction.warningTitle": "エラー", "app.containers.BtnMoreBookingAction.confirmTitle": "予約ステータス変更", "app.containers.BtnMoreBookingAction.changeBookingStatus": "「{status}」に変更する", "app.containers.BtnMoreBookingAction.confirmChangeStatus": "この予約を「{status}」に変更しますか？ ", "app.containers.BtnMoreBookingAction.changeStatusSuccessMessage": "ステータスの変更が完了しました。", "app.containers.UpdateTherapist.title": "基本情報 - 編集"}